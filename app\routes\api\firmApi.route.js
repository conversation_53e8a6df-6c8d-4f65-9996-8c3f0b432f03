import express from "express";
import {
  firm<PERSON>pi<PERSON>ogin,
  firmApi<PERSON>ddUser,
  firmApiCreateCase,
  firmApiUpdateCase,
  firmApiExportCaseData,
  firmApiGetCases,
  firmApiGetCase,
  firmApiGetQuestionnaire,
  firmApiGetLawFirm
} from "#controllers/firmApi.controller.js";
import { apiTokenAuth, requireAdminAPI, routeProtection } from "#middlewares/authMiddleware.js";

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Firm API
 *   description: |
 *     Law firm-level API operations with two-step authentication:
 *     1. Use API token to login via /api/firm/login
 *     2. Use returned JWT token for all other operations
 */

/**
 * @swagger
 * /api/firm/login:
 *   post:
 *     summary: Two-layer authentication - API token + user credentials
 *     description: Requires both API token (for firm authentication) and email/password (for user authentication)
 *     tags: [Firm API]
 *     security:
 *       - apiTokenAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 format: password
 *                 description: User's password
 *                 example: "userpassword123"
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Login successful"
 *                 data:
 *                   type: object
 *                   properties:
 *                     access_token:
 *                       type: string
 *                       description: JWT token for the authenticated user
 *                     user:
 *                       type: object
 *                       properties:
 *                         user_id:
 *                           type: integer
 *                         email:
 *                           type: string
 *                         role:
 *                           type: string
 *                         lf_id:
 *                           type: integer
 *                         first_name:
 *                           type: string
 *                         last_name:
 *                           type: string
 *       400:
 *         description: Missing email or password
 *       401:
 *         description: Invalid email or password
 *       403:
 *         description: User does not belong to authorized law firm
 */
// Login route uses API token authentication
router.post("/login", apiTokenAuth, requireAdminAPI, firmApiLogin);

// All other routes use JWT authentication
router.use(routeProtection);

/**
 * @swagger
 * /api/firm/users:
 *   post:
 *     summary: Add user to law firm
 *     tags: [Firm API]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - first_name
 *               - last_name
 *               - role
 *             properties:
 *               email:
 *                 type: string
 *               first_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               role:
 *                 type: string
 *                 enum: ["2", "3", "4"]
 *                 description: "2=LawfirmAdmin, 3=LawfirmUser, 4=Client"
 *               org_type:
 *                 type: string
 *               org_name:
 *                 type: string
 *               title:
 *                 type: string
 *               middle_name:
 *                 type: string
 *               dob:
 *                 type: string
 *                 format: date
 *               gender:
 *                 type: string
 *               home_phone:
 *                 type: string
 *               mb_phone:
 *                 type: string
 *               wrk_phone:
 *                 type: string
 *               adr_1:
 *                 type: string
 *               adr_2:
 *                 type: string
 *               adr_3:
 *                 type: string
 *               state:
 *                 type: string
 *               town:
 *                 type: string
 *               country:
 *                 type: string
 *               post_code:
 *                 type: string
 *               pms_id:
 *                 type: string
 *     responses:
 *       201:
 *         description: User added successfully
 */
router.post("/users", firmApiAddUser);

/**
 * @swagger
 * /api/firm/cases:
 *   post:
 *     summary: Create a new case
 *     tags: [Firm API]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - case_name
 *               - user_id
 *               - qtn_id
 *             properties:
 *               case_name:
 *                 type: string
 *               desc:
 *                 type: string
 *               user_id:
 *                 type: integer
 *               qtn_id:
 *                 type: integer
 *               case_id_pms:
 *                 type: string
 *               token_:
 *                 type: object
 *                 description: Optional token replacement object for questionnaire templates (e.g., {"{{custom_field}}": "value"})
 *     responses:
 *       201:
 *         description: Case created successfully
 */
router.post("/cases", firmApiCreateCase);

/**
 * @swagger
 * /api/firm/cases:
 *   get:
 *     summary: Get cases for law firm
 *     tags: [Firm API]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: size
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Cases retrieved successfully
 */
router.get("/cases", firmApiGetCases);

/**
 * @swagger
 * /api/firm/case:
 *   post:
 *     summary: Get specific case details
 *     tags: [Firm API]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - case_id
 *             properties:
 *               case_id:
 *                 type: string
 *                 description: Case ID to retrieve
 *     responses:
 *       200:
 *         description: Case details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     case:
 *                       type: object
 *                       description: Case information
 *                     user:
 *                       type: object
 *                       description: User information associated with the case
 *       400:
 *         description: Missing case_id or invalid request
 *       403:
 *         description: Case does not belong to your law firm
 *       404:
 *         description: Case not found
 */
router.post("/case", firmApiGetCase);

/**
 * @swagger
 * /api/firm/questionnaire:
 *   post:
 *     summary: Get specific questionnaire details
 *     tags: [Firm API]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - qtn_id
 *             properties:
 *               qtn_id:
 *                 type: string
 *                 description: Questionnaire ID to retrieve
 *     responses:
 *       200:
 *         description: Questionnaire details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   description: Questionnaire information
 *       400:
 *         description: Missing qtn_id or invalid request
 *       403:
 *         description: Questionnaire does not belong to your law firm
 *       404:
 *         description: Questionnaire not found
 */
router.post("/questionnaire", firmApiGetQuestionnaire);

/**
 * @swagger
 * /api/firm/cases/update:
 *   post:
 *     summary: Update a case (matches normal implementation)
 *     tags: [Firm API]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *             properties:
 *               id:
 *                 type: string
 *                 description: Case ID
 *               status:
 *                 type: integer
 *                 description: New case status
 *               case_name:
 *                 type: string
 *                 description: Case name
 *               desc:
 *                 type: string
 *                 description: Case description
 *     responses:
 *       200:
 *         description: Case updated successfully
 */
router.post("/cases/update", firmApiUpdateCase);

/**
 * @swagger
 * /api/firm/cases/export:
 *   post:
 *     summary: Export case data (matches normal implementation)
 *     tags: [Firm API]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - case_id
 *             properties:
 *               case_id:
 *                 type: string
 *                 description: Case ID
 *               format:
 *                 type: string
 *                 enum: [json, csv, excel]
 *                 default: json
 *                 description: Export format
 *               check:
 *                 type: integer
 *                 enum: [1, 2]
 *                 default: 1
 *                 description: Excel spreadsheet type (only for excel format)
 *     responses:
 *       200:
 *         description: Case data exported successfully
 */
router.post("/cases/export", firmApiExportCaseData);

/**
 * @swagger
 * /api/firm/lawfirm:
 *   get:
 *     summary: Get law firm information (for debugging)
 *     tags: [Firm API]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Law firm information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     lf_id:
 *                       type: integer
 *                     lf_org_name:
 *                       type: string
 *                     prefix:
 *                       type: string
 *                     status:
 *                       type: integer
 *       404:
 *         description: Law firm not found
 */
router.get("/lawfirm", firmApiGetLawFirm);

export default router;
