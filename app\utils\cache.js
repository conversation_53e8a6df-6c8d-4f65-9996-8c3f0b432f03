import Redis from 'ioredis';
import logger from '#logger';

// Redis client configuration
let redisClient = null;

const initializeRedis = () => {
  if (!redisClient) {
    try {
      redisClient = new Redis({
        host: process.env.REDIS_HOST || 'localhost',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD || undefined,
        db: process.env.REDIS_DB || 0,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        // Connection timeout
        connectTimeout: 10000,
        // Command timeout
        commandTimeout: 5000,
      });

      redisClient.on('connect', () => {
        logger.info('Redis connected successfully');
      });

      redisClient.on('error', (err) => {
        logger.error('Redis connection error:', err);
      });

      redisClient.on('close', () => {
        logger.warn('Redis connection closed');
      });

    } catch (error) {
      logger.error('Failed to initialize Redis:', error);
      redisClient = null;
    }
  }
  return redisClient;
};

// Cache key generators
const generateCacheKey = (prefix, ...args) => {
  return `${prefix}:${args.join(':')}`;
};

// Cache TTL configurations (in seconds)
const CACHE_TTL = {
  TEMPLATE: 3600,        // 1 hour - templates don't change frequently
  QUESTIONNAIRE: 1800,   // 30 minutes - questionnaires may change more often
  CASES: 300,           // 5 minutes - cases change frequently
  USER: 900,            // 15 minutes - user data
  LAW_FIRM: 3600,       // 1 hour - law firm data
};

// Generic cache wrapper function
const withCache = async (cacheKey, ttl, fetchFunction, forceRefresh = false) => {
  const redis = initializeRedis();
  
  // If Redis is not available, execute function directly
  if (!redis) {
    logger.warn('Redis not available, executing function without cache');
    return await fetchFunction();
  }

  try {
    // Check if we should skip cache
    if (!forceRefresh) {
      const cachedData = await redis.get(cacheKey);
      if (cachedData) {
        logger.debug(`Cache hit for key: ${cacheKey}`);
        return JSON.parse(cachedData);
      }
    }

    // Cache miss or force refresh - fetch fresh data
    logger.debug(`Cache miss for key: ${cacheKey}, fetching fresh data`);
    const freshData = await fetchFunction();

    // Store in cache
    if (freshData) {
      await redis.setex(cacheKey, ttl, JSON.stringify(freshData));
      logger.debug(`Data cached for key: ${cacheKey}, TTL: ${ttl}s`);
    }

    return freshData;
  } catch (error) {
    logger.error(`Cache operation failed for key: ${cacheKey}`, error);
    // Fallback to direct function execution
    return await fetchFunction();
  }
};

// Cache invalidation functions
const invalidateCache = async (pattern) => {
  const redis = initializeRedis();
  if (!redis) return;

  try {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
      logger.info(`Invalidated ${keys.length} cache keys matching pattern: ${pattern}`);
    }
  } catch (error) {
    logger.error(`Failed to invalidate cache pattern: ${pattern}`, error);
  }
};

// Specific cache functions for our optimized services

/**
 * Cache wrapper for getTemplate function
 */
export const cacheTemplate = async (templateId, fetchFunction, forceRefresh = false) => {
  const cacheKey = generateCacheKey('template', templateId);
  return withCache(cacheKey, CACHE_TTL.TEMPLATE, fetchFunction, forceRefresh);
};

/**
 * Cache wrapper for getQuestionnaire function
 */
export const cacheQuestionnaire = async (questionnaireId, fetchFunction, forceRefresh = false) => {
  const cacheKey = generateCacheKey('questionnaire', questionnaireId);
  return withCache(cacheKey, CACHE_TTL.QUESTIONNAIRE, fetchFunction, forceRefresh);
};

/**
 * Cache wrapper for getCases function
 */
export const cacheCases = async (lf_id, page, size, status, user_id, fetchFunction, forceRefresh = false) => {
  const cacheKey = generateCacheKey('cases', lf_id, page, size, status || 'all', user_id || 'all');
  return withCache(cacheKey, CACHE_TTL.CASES, fetchFunction, forceRefresh);
};

/**
 * Cache wrapper for user data
 */
export const cacheUser = async (userId, fetchFunction, forceRefresh = false) => {
  const cacheKey = generateCacheKey('user', userId);
  return withCache(cacheKey, CACHE_TTL.USER, fetchFunction, forceRefresh);
};

/**
 * Cache wrapper for law firm data
 */
export const cacheLawFirm = async (lawFirmId, fetchFunction, forceRefresh = false) => {
  const cacheKey = generateCacheKey('lawfirm', lawFirmId);
  return withCache(cacheKey, CACHE_TTL.LAW_FIRM, fetchFunction, forceRefresh);
};

// Cache invalidation functions for specific entities

/**
 * Invalidate template cache when template is updated
 */
export const invalidateTemplateCache = async (templateId) => {
  await invalidateCache(`template:${templateId}`);
  // Also invalidate related questionnaires
  await invalidateCache(`questionnaire:*`);
};

/**
 * Invalidate questionnaire cache when questionnaire is updated
 */
export const invalidateQuestionnaireCache = async (questionnaireId) => {
  await invalidateCache(`questionnaire:${questionnaireId}`);
};

/**
 * Invalidate cases cache when cases are updated
 */
export const invalidateCasesCache = async (lf_id) => {
  await invalidateCache(`cases:${lf_id}:*`);
};

/**
 * Invalidate user cache when user is updated
 */
export const invalidateUserCache = async (userId) => {
  await invalidateCache(`user:${userId}`);
};

/**
 * Invalidate law firm cache when law firm is updated
 */
export const invalidateLawFirmCache = async (lawFirmId) => {
  await invalidateCache(`lawfirm:${lawFirmId}`);
  // Also invalidate related cases and questionnaires
  await invalidateCache(`cases:${lawFirmId}:*`);
  await invalidateCache(`questionnaire:*`);
};

// Health check function
export const checkCacheHealth = async () => {
  const redis = initializeRedis();
  if (!redis) return { status: 'unavailable' };

  try {
    const pong = await redis.ping();
    return { 
      status: 'healthy', 
      response: pong,
      memory: await redis.memory('usage')
    };
  } catch (error) {
    return { 
      status: 'error', 
      error: error.message 
    };
  }
};

// Cleanup function
export const closeRedisConnection = async () => {
  if (redisClient) {
    await redisClient.quit();
    redisClient = null;
    logger.info('Redis connection closed');
  }
};

// Export the cache utility functions
export default {
  cacheTemplate,
  cacheQuestionnaire,
  cacheCases,
  cacheUser,
  cacheLawFirm,
  invalidateTemplateCache,
  invalidateQuestionnaireCache,
  invalidateCasesCache,
  invalidateUserCache,
  invalidateLawFirmCache,
  checkCacheHealth,
  closeRedisConnection
};
