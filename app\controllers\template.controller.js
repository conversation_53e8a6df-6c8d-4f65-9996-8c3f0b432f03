import { templateService } from "#services";
import { catchAsync } from "#utils";
import { roles } from "#middlewares/roles.js";

export const createTemplate = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    const { tem_name, tem_desc } = req.body;
    try {
      const template = await templateService.createTemplate(tem_name, tem_desc);
      return res.status(200).json({
        success: true,
        message: "Template created",
        template,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const getTemplate = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    const { id } = req.body;
    try {
      const template = await templateService.getTemplate(id);
      return res.status(200).json({
        success: true,
        message: "Template fetched",
        template,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});
export const getAllTemplate = catchAsync(async (req, res) => {
  const role = req.user.role;
  const { page, size, keyword } = req.body;
  if (role == roles.Admin) {
    try {
      const template = await templateService.getAllTemplate(
        page,
        size,
        keyword
      );
      return res.status(200).json({
        success: true,
        message: "Template fetched",
        template,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const deleteTemplate = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    const { id } = req.body;
    try {
      const template = await templateService.deleteTemplate(id);
      return res.status(200).json({
        success: true,
        message: "Template deleted",
        template,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const updateTemplate = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    let check = await templateService.getTemplate(req.body.id);
    if (check.length == 0) {
      return res.status(400).json({
        success: false,
        message: "Template does not exist",
      });
    } else {
      try {
        const template = await templateService.updateTemplate(
          req.body,
          req.user.email
        );
        return res.status(200).json({
          success: true,
          message: "Template updated",
        });
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const updateTemplateStatus = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    let check = await templateService.getTemplate(req.body.id);
    if (check.length == 0) {
      return res.status(400).json({
        success: false,
        message: "Template does not exist",
      });
    } else {
      try {
        const template = await templateService.updateTemplateStatus(
          req.body,
          req.user.email
        );
        return res.status(200).json({
          success: true,
          message: "Template updated",
        });
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const createTemplateFromJson = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    try {
      const template = await templateService.createTemplateFromJson(
        req.body,
        req.user.email
      );
      return res.status(200).json({
        success: true,
        message: "Template created",
        template,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const createTemplateAnswer = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    try {
      const template = await templateService.createTemplateAnswer(req.body);
      return res.status(200).json({
        success: true,
        message: "Template answer created",
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const getTemplateAnswer = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin || role == roles.LawfirmSuperAdmin) {
    const { id } = req.body;
    try {
      const template = await templateService.getTemplateAnswer(id);
      return res.status(200).json({
        success: true,
        message: "Template answer fetched",
        template,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});
export const getAllTemplateAnswer = catchAsync(async (req, res) => {
  const role = req.user.role;
  const { page, size, keyword, status } = req.body;
  if (role == roles.Admin || role == roles.LawfirmSuperAdmin) {
    try {
      const template = await templateService.getAllTemplateAnswer(
        page,
        size,
        keyword,
        status
      );
      return res.status(200).json({
        success: true,
        message: "Template answer fetched",
        template,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const deleteTemplateAnswer = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    const { id } = req.body;
    try {
      const template = await templateService.deleteTemplateAnswer(id);
      return res.status(200).json({
        success: true,
        message: "Template answer deleted",
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});
export const updateTemplateAnswerStatus = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    let check = await templateService.getTemplateAnswer(req.body.id);
    if (check.length == 0) {
      return res.status(400).json({
        success: false,
        message: "Template answer does not exist",
      });
    } else {
      try {
        const template = await templateService.updateTemplateAnswerStatus(
          req.body
        );
        return res.status(200).json({
          success: true,
          message: "Template answer updated",
        });
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});
export const updateTemplateAnswer = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    let check = await templateService.getTemplateAnswer(req.body.id);
    if (check.length == 0) {
      return res.status(400).json({
        success: false,
        message: "Template answer does not exist",
      });
    } else {
      try {
        const template = await templateService.updateTemplateAnswer(req.body);
        return res.status(200).json({
          success: true,
          message: "Template answer updated",
        });
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const createLink = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    try {
      const template = await templateService.createLink(req.body);
      return res.status(200).json({
        success: true,
        message: "Link created",
        template,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});
export const modifyQuestion = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    try {
      const template = await templateService.modifyQuestion(req.body);
      return res.status(200).json({
        success: true,
        message: "Question Edited",
        template,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});
