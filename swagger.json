{"openapi": "3.0.0", "info": {"title": "Propero Node Server API", "version": "1.0.0", "description": "API documentation for Propero Node Server"}, "servers": [{"url": "http://localhost:8989", "description": "Development server"}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token for standard authentication"}, "apiTokenAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "API Token", "description": "API token for firm-level access (format: propero_xxxxxxxx_...)"}}, "schemas": {"ApiToken": {"type": "object", "required": ["token_name", "user_id"], "properties": {"token_name": {"type": "string", "description": "Descriptive name for the token"}, "user_id": {"type": "integer", "description": "ID of the AdminAPI user"}, "lf_id": {"type": "integer", "description": "Law firm ID (Admin only)"}, "expires_at": {"type": "string", "format": "date-time", "description": "Token expiration date (optional)"}, "permissions": {"type": "object", "description": "Token permissions (optional)"}}}, "PaymentResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Success"}, "data": {"type": "object"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Permission denied!"}}}, "CreatePaymentRequest": {"type": "object", "required": ["amount", "description", "mandate"], "properties": {"amount": {"type": "number", "description": "Payment amount"}, "description": {"type": "string", "description": "Payment description"}, "mandate": {"type": "string", "description": "Mandate ID"}}}, "GetHistoryRequest": {"type": "object", "required": ["page", "size"], "properties": {"page": {"type": "number", "description": "Page number for pagination"}, "size": {"type": "number", "description": "Number of items per page"}, "keyword": {"type": "string", "description": "Search keyword"}, "startDate": {"type": "string", "format": "date", "description": "Start date for filtering"}, "endDate": {"type": "string", "format": "date", "description": "End date for filtering"}, "lf_id": {"type": "string", "description": "Law firm ID (required for Admin role)"}}}, "Case": {"type": "object", "properties": {"case_id": {"type": "string", "description": "Unique identifier for the case"}, "case_name": {"type": "string", "description": "Name of the case"}, "status": {"type": "integer", "description": "Current status of the case"}, "user_id": {"type": "string", "description": "User associated with the case"}, "lf_id": {"type": "string", "description": "Law firm identifier"}, "desc": {"type": "string", "description": "Case description"}}}, "CaseResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object"}}}, "GraphCredentials": {"type": "object", "required": ["lf_id", "tenant_id", "client_id", "client_secret"], "properties": {"lf_id": {"type": "integer", "description": "Law firm ID"}, "tenant_id": {"type": "string", "description": "Microsoft Entra ID tenant ID"}, "client_id": {"type": "string", "description": "Application (client) ID"}, "client_secret": {"type": "string", "description": "Client secret value"}, "is_active": {"type": "boolean", "description": "Whether credentials are active"}}}, "Modal": {"type": "object", "properties": {"id": {"type": "string", "description": "Modal unique identifier"}, "name": {"type": "string", "description": "Modal name"}, "type": {"type": "string", "description": "Modal type"}, "status": {"type": "string", "enum": ["active", "inactive"], "description": "Modal status"}, "content": {"type": "object", "description": "Modal content and configuration"}}}, "ModalAnswer": {"type": "object", "properties": {"modalId": {"type": "string", "description": "Modal identifier"}, "answers": {"type": "object", "description": "Answer content"}}}, "ModalType": {"type": "object", "properties": {"id": {"type": "string", "description": "Type identifier"}, "name": {"type": "string", "description": "Type name"}}}, "Questionnaire": {"type": "object", "properties": {"id": {"type": "integer", "description": "Questionnaire ID"}, "qtn_name": {"type": "string", "description": "Questionnaire name"}, "tem_id": {"type": "integer", "description": "Template ID"}, "lf_id": {"type": "integer", "description": "Law firm ID"}, "price": {"type": "number", "description": "Price of questionnaire"}, "status": {"type": "integer", "description": "Status of questionnaire (0=draft, 1=active, 2=inactive, 3=pending)"}}}, "Error": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string"}}}, "OTPRequest": {"type": "object", "properties": {"email": {"type": "string", "description": "User's email address"}, "token": {"type": "string", "description": "Optional token for authentication"}}}, "OTPVerifyRequest": {"type": "object", "properties": {"email": {"type": "string", "description": "User's email address"}, "otp": {"type": "string", "description": "OTP code to verify"}, "token": {"type": "string", "description": "Optional token for authentication"}}}, "CaseVerifyRequest": {"type": "object", "required": ["case_id"], "properties": {"case_id": {"type": "string", "description": "ID of the case to verify"}}}, "Template": {"type": "object", "properties": {"id": {"type": "string", "description": "Template unique identifier"}, "tem_name": {"type": "string", "description": "Template name"}, "tem_desc": {"type": "string", "description": "Template description"}, "status": {"type": "string", "enum": ["active", "inactive"], "description": "Template status"}}}, "TemplateAnswer": {"type": "object", "properties": {"id": {"type": "string", "description": "Answer unique identifier"}, "template_id": {"type": "string", "description": "Reference to template"}, "answers": {"type": "object", "description": "Answer content"}, "status": {"type": "string", "enum": ["active", "inactive"], "description": "Answer status"}}}, "PaginationRequest": {"type": "object", "properties": {"page": {"type": "number", "description": "Page number"}, "size": {"type": "number", "description": "Items per page"}, "keyword": {"type": "string", "description": "Search keyword"}}}, "User": {"type": "object", "properties": {"user_id": {"type": "string"}, "email": {"type": "string"}, "status": {"type": "integer"}, "role": {"type": "string"}, "lf_id": {"type": "string"}, "title": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "mb_phone": {"type": "string"}}}}}, "paths": {"/": {"get": {"summary": "Get server status", "description": "Returns server status information including deployment time and environment", "tags": ["Server"], "responses": {"200": {"description": "Server status information", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Server is running..."}, "deployTime": {"type": "string", "format": "date-time"}, "env": {"type": "string", "example": "development"}, "CICD": {"type": "string", "nullable": true}}}}}}}}}, "/private/api-tokens": {"post": {"summary": "Create a new API token", "tags": ["API Tokens"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiToken"}}}}, "responses": {"201": {"description": "API token created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "token": {"type": "string", "description": "The actual token (only shown once)"}, "token_name": {"type": "string"}, "prefix": {"type": "string"}, "expires_at": {"type": "string"}, "created_at": {"type": "string"}}}}}}}}}}, "get": {"summary": "Get API tokens for law firm", "tags": ["API Tokens"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "API tokens retrieved successfully"}}}}, "/private/api-tokens/{lf_id}": {"get": {"summary": "Get API tokens for specific law firm (Admin only)", "tags": ["API Tokens"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "lf_id", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "API tokens retrieved successfully"}}}}, "/private/api-tokens/{token_id}/revoke": {"put": {"summary": "Revoke an API token", "tags": ["API Tokens"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "token_id", "required": true, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"lf_id": {"type": "integer", "description": "Law firm ID (Admin only)"}}}}}}, "responses": {"200": {"description": "API token revoked successfully"}}}}, "/private/billing/getAllPayment": {"post": {"summary": "Get all payments for a law firm", "tags": ["Billing"], "security": [{"bearerAuth": []}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "properties": {"mandate": {"type": "string", "description": "Mandate ID (required for Ad<PERSON> role only)"}}}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentResponse"}}}}, "500": {"description": "Permission denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/private/billing/getAllHistory": {"post": {"summary": "Get payment history with pagination and filtering", "tags": ["Billing"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetHistoryRequest"}}}}, "responses": {"200": {"description": "Successfully retrieved history", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentResponse"}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Permission denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/private/billing/createPayment": {"post": {"summary": "Create a new payment", "tags": ["Billing"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentRequest"}}}}, "responses": {"200": {"description": "Payment created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentResponse"}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/private/case/createCase": {"post": {"summary": "Create a new case", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["case_name", "cases", "qtn_id"], "properties": {"case_name": {"type": "string"}, "cases": {"type": "array"}, "qtn_id": {"type": "string"}, "user_id": {"type": "string"}, "case_id_pms": {"type": "string"}, "desc": {"type": "string"}, "token_": {"type": "string"}}}}}}, "responses": {"200": {"description": "Case created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "case_id": {"type": "string"}}}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/private/case/getAllCase": {"post": {"summary": "Get all cases with filtering and pagination", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["page", "size"], "properties": {"page": {"type": "number"}, "size": {"type": "number"}, "keyword": {"type": "string"}, "status": {"type": "number"}, "user_id": {"type": "string"}, "lf_id": {"type": "string"}}}}}}, "responses": {"200": {"description": "Cases retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaseResponse"}}}}}}}, "/private/case/getCase": {"post": {"summary": "Get case details by ID", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "string"}}}}}}, "responses": {"200": {"description": "Case details retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaseResponse"}}}}}}}, "/private/case/deleteCase": {"post": {"summary": "Delete a case", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "string"}}}}}}, "responses": {"200": {"description": "Case deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaseResponse"}}}}}}}, "/private/case/updateAnswer": {"post": {"summary": "Update case answers", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id", "answer"], "properties": {"id": {"type": "string"}, "answer": {"type": "array"}}}}}}, "responses": {"200": {"description": "Answers updated successfully"}}}}, "/private/case/updateAnswerSubmitted": {"post": {"summary": "Update submitted answers", "tags": ["Cases"], "security": [{"bearerAuth": []}]}}, "/private/case/updateCaseStatus": {"post": {"summary": "Update case status", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["status", "id"], "properties": {"status": {"type": "number"}, "id": {"type": "string"}}}}}}}}, "/private/case/submitCase": {"post": {"summary": "Submit a case", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "string"}, "user_id": {"type": "string"}}}}}}, "responses": {"200": {"description": "Case submitted successfully"}}}}, "/private/case/getCaseSumByUserId": {"get": {"summary": "Get case summary by user ID with pagination", "tags": ["Cases"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "number"}, "required": true}, {"in": "query", "name": "size", "schema": {"type": "number"}, "required": true}, {"in": "query", "name": "keyword", "schema": {"type": "string"}}]}}, "/private/case/download": {"post": {"summary": "Download case PDF", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["case_id"], "properties": {"case_id": {"type": "string"}}}}}}, "responses": {"200": {"description": "Returns PDF file", "content": {"application/pdf": {"schema": {"type": "string", "format": "binary"}}}}}}, "get": {"summary": "Download case PDF after submission", "tags": ["Cases"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "case_id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns PDF file", "content": {"application/pdf": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/private/case/grantPermission": {"post": {"summary": "Grant case access permission to a user", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["case_id", "email"], "properties": {"case_id": {"type": "string"}, "email": {"type": "string"}}}}}}, "responses": {"200": {"description": "Permission granted successfully"}}}}, "/private/case/denyPermission": {"post": {"summary": "Revoke case access permission from a user", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["case_id", "email"], "properties": {"case_id": {"type": "string"}, "email": {"type": "string"}}}}}}}}, "/private/case/uploadDocument": {"post": {"summary": "Upload a document for a case", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "case_id": {"type": "string"}}}}}}}}, "/private/case/downloadDocument": {"get": {"summary": "Download a case document", "tags": ["Cases"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "file", "required": true, "schema": {"type": "string"}}, {"in": "query", "name": "case_id", "required": true, "schema": {"type": "string"}}]}}, "/private/case/exportPDFToPMS": {"post": {"summary": "Export case PDF to PMS", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id", "lf_id"], "properties": {"id": {"type": "string"}, "case_id_pms": {"type": "string"}, "lf_id": {"type": "string"}}}}}}}}, "/private/case/exportDataToPMS": {"post": {"summary": "Export case data to PMS", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id", "lf_id"], "properties": {"id": {"type": "string"}, "case_id_pms": {"type": "string"}, "lf_id": {"type": "string"}, "qtn_id": {"type": "string"}}}}}}}}, "/private/case/generateSpreadsheet": {"post": {"summary": "Generate an Excel spreadsheet for a case", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["case_id", "check"], "properties": {"case_id": {"type": "string"}, "check": {"type": "number", "description": "Type of spreadsheet to generate (1 or 2)"}}}}}}}}, "/private/case/getCaseByQuestionaireId": {"post": {"summary": "Get cases by questionnaire ID", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["qtn_id"], "properties": {"qtn_id": {"type": "string"}}}}}}}}, "/private/case/getCaseByUserId": {"post": {"summary": "Get cases by user ID", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["user_id"], "properties": {"user_id": {"type": "string"}}}}}}}}, "/private/case/callMe": {"post": {"summary": "Request a callback for a case", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["case_id"], "properties": {"case_id": {"type": "string"}, "phone": {"type": "string"}}}}}}}}, "/private/case/getAllGranted": {"post": {"summary": "Get all cases with granted access", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"page": {"type": "number"}, "size": {"type": "number"}}}}}}}}, "/private/case/getModalAnswer": {"post": {"summary": "Get modal answer details", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["case_id"], "properties": {"case_id": {"type": "string"}}}}}}}}, "/private/case/getClientPMS": {"get": {"summary": "Get case ID from PMS", "tags": ["Cases"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "client_id", "required": true, "schema": {"type": "string"}}]}}, "/private/case/getDetailCase": {"post": {"summary": "Get detailed case answers by ID", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["case_id"], "properties": {"case_id": {"type": "string"}}}}}}}}, "/private/case/createCaseFromPMS": {"post": {"summary": "Create a new case from PMS data", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["case_id_pms", "lf_id"], "properties": {"case_id_pms": {"type": "string"}, "lf_id": {"type": "string"}}}}}}}}, "/private/case/getPartiesForAssignQtn": {"post": {"summary": "Get parties available for questionnaire assignment", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["case_id"], "properties": {"case_id": {"type": "string"}}}}}}}}, "/private/case/updateEmailorPhoneParty": {"post": {"summary": "Update party email or phone number", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["case_id", "party_id"], "properties": {"case_id": {"type": "string"}, "party_id": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}}}}}}}}, "/private/case/assignQtnToParty": {"post": {"summary": "Assign questionnaire to a party", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["case_id", "party_id", "qtn_id"], "properties": {"case_id": {"type": "string"}, "party_id": {"type": "string"}, "qtn_id": {"type": "string"}}}}}}}}, "/private/case/checkInfoParty": {"post": {"summary": "Check party information", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["case_id", "party_id"], "properties": {"case_id": {"type": "string"}, "party_id": {"type": "string"}}}}}}}}, "/private/case/updateStatusExported": {"post": {"summary": "Update case export status", "tags": ["Cases"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["case_id", "status"], "properties": {"case_id": {"type": "string"}, "status": {"type": "number"}}}}}}}}, "/private/customMail/editStatement": {"post": {"summary": "Edit law firm statement", "description": "Update the statement for a law firm. Requires LawfirmSuperAdmin role for the specific law firm or Admin role.", "tags": ["Custom Mail"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["lf_id", "lf_statement"], "properties": {"lf_id": {"type": "integer", "description": "Law firm ID"}, "lf_statement": {"type": "string", "description": "New statement text"}}}}}}, "responses": {"200": {"description": "Statement updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Success"}}}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Please check again"}}}}}}, "500": {"description": "Permission denied or server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Permission denied!"}}}}}}}}}, "/private/customMail/editTemplateMail": {"post": {"summary": "Edit email template", "description": "Update the email template for a law firm. Requires LawfirmSuperAdmin role for the specific law firm.", "tags": ["Custom Mail"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["lf_id", "mail_type"], "properties": {"logo_name": {"type": "string", "description": "Name of the logo file"}, "title": {"type": "string", "description": "Email title"}, "header": {"type": "string", "description": "Email header content"}, "body": {"type": "string", "description": "Email body content"}, "footer": {"type": "string", "description": "Email footer content"}, "logo": {"type": "string", "description": "Logo URL or base64 string"}, "lf_id": {"type": "integer", "description": "Law firm ID"}, "mail_type": {"type": "string", "description": "Type of email template"}, "remind_time": {"type": "integer", "description": "Reminder time in days"}}}}}}, "responses": {"200": {"description": "Template updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Success"}}}}}}, "400": {"description": "Invalid request"}, "500": {"description": "Permission denied or server error"}}}}, "/private/customMail/getTemplateMail": {"get": {"summary": "Get email template", "description": "Retrieve the email template for a law firm. Requires LawfirmSuperAdmin role for the specific law firm.", "tags": ["Custom Mail"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "lf_id", "required": true, "schema": {"type": "integer"}, "description": "Law firm ID"}, {"in": "query", "name": "mail_type", "required": true, "schema": {"type": "string"}, "description": "Type of email template"}], "responses": {"200": {"description": "Template retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Template Mail fetched"}, "data": {"type": "object", "description": "Template data"}}}}}}, "500": {"description": "Permission denied or server error"}}}}, "/private/customMail/uploadLogo": {"post": {"summary": "Upload law firm logo", "description": "Upload a logo for the law firm's email templates. Requires LawfirmSuperAdmin role for the specific law firm.", "tags": ["Custom Mail"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["lf_id", "file"], "properties": {"lf_id": {"type": "integer", "description": "Law firm ID"}, "file": {"type": "string", "format": "binary", "description": "Logo file (base64 encoded)"}}}}}}, "responses": {"200": {"description": "Logo uploaded successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Logo uploaded"}, "data": {"type": "object", "description": "Upload result data"}}}}}}, "400": {"description": "No file uploaded"}, "500": {"description": "Permission denied or server error"}}}}, "/private/graph-credentials": {"post": {"summary": "Add Microsoft Graph credentials for a law firm", "description": "Add Microsoft Graph API credentials for a law firm. Requires Admin or LawfirmSuperAdmin role.", "tags": ["Graph Credentials"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GraphCredentials"}}}}, "responses": {"201": {"description": "Graph credentials added successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad request"}, "403": {"description": "Permission denied"}}}}, "/private/graph-credentials/all": {"get": {"summary": "Get all law firms with Graph credentials (Admin only)", "description": "Retrieve all law firms that have Microsoft Graph credentials configured. Admin only.", "tags": ["Graph Credentials"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "All Graph credentials retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "object"}}}}}}}, "403": {"description": "Permission denied"}}}}, "/private/graph-credentials/{lf_id}": {"get": {"summary": "Get Microsoft Graph credentials for a law firm", "description": "Retrieve Microsoft Graph credentials for a specific law firm. Excludes client secret.", "tags": ["Graph Credentials"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "lf_id", "required": true, "schema": {"type": "integer"}, "description": "Law firm ID"}], "responses": {"200": {"description": "Graph credentials retrieved successfully"}, "403": {"description": "Permission denied"}, "404": {"description": "Credentials not found"}}}, "put": {"summary": "Update Microsoft Graph credentials for a law firm", "description": "Update Microsoft Graph credentials for a specific law firm. Requires Admin or LawfirmSuperAdmin role.", "tags": ["Graph Credentials"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "lf_id", "required": true, "schema": {"type": "integer"}, "description": "Law firm ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"tenant_id": {"type": "string"}, "client_id": {"type": "string"}, "client_secret": {"type": "string"}, "is_active": {"type": "boolean"}}}}}}, "responses": {"200": {"description": "Graph credentials updated successfully"}, "400": {"description": "Bad request"}, "403": {"description": "Permission denied"}}}, "delete": {"summary": "Delete Microsoft Graph credentials for a law firm", "description": "Deactivate Microsoft Graph credentials for a specific law firm. Requires Admin or LawfirmSuperAdmin role.", "tags": ["Graph Credentials"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "lf_id", "required": true, "schema": {"type": "integer"}, "description": "Law firm ID"}], "responses": {"200": {"description": "Graph credentials deleted successfully"}, "400": {"description": "Bad request"}, "403": {"description": "Permission denied"}}}}, "/private/graph-credentials/{lf_id}/test": {"post": {"summary": "Test Microsoft Graph API connection", "description": "Test the Microsoft Graph API connection for a law firm's credentials.", "tags": ["Graph Credentials"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "lf_id", "required": true, "schema": {"type": "integer"}, "description": "Law firm ID"}], "responses": {"200": {"description": "Connection test completed", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"sendgrid": {"type": "object"}, "graph": {"type": "object"}}}}}}}}, "403": {"description": "Permission denied"}}}}, "/private/graph-credentials/{lf_id}/provider-info": {"get": {"summary": "Get mail provider information for a law firm", "description": "Get information about which mail provider will be used for a law firm.", "tags": ["Graph Credentials"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "lf_id", "required": true, "schema": {"type": "integer"}, "description": "Law firm ID"}], "responses": {"200": {"description": "Provider info retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"primaryProvider": {"type": "string"}, "fallbackProvider": {"type": "string"}, "graphConfigured": {"type": "boolean"}}}}}}}}, "403": {"description": "Permission denied"}}}}, "/private/mail/sendTempPassword": {"post": {"summary": "Send temporary password to user's email", "tags": ["Mail"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "password", "first_name"], "properties": {"email": {"type": "string", "format": "email", "description": "User's email address"}, "password": {"type": "string", "description": "Temporary password to send"}, "first_name": {"type": "string", "description": "User's first name for email personalization"}}}}}}, "responses": {"200": {"description": "Temporary password sent successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Password sent"}}}}}}, "400": {"description": "Error sending temporary password", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Error sending temp password"}}}}}}, "500": {"description": "Unauthorized access", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized"}}}}}}}}}, "/private/mail/getMailType": {"get": {"summary": "Get available mail types", "tags": ["Mail"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Mail types retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Mail type fetched"}, "mailType": {"type": "array", "items": {"type": "object"}}}}}}}}}}, "/private/modal/createModal": {"post": {"summary": "Create a new modal", "tags": ["Modal"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "content": {"type": "object"}}}}}}, "responses": {"200": {"description": "Modal created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "modal": {"$ref": "#/components/schemas/Modal"}}}}}}, "400": {"description": "Invalid request"}, "500": {"description": "Permission denied or server error"}}}}, "/private/modal/getModal": {"post": {"summary": "Get modal by ID", "tags": ["Modal"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "description": "Modal ID"}}}}}}, "responses": {"200": {"description": "Modal details fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "modal": {"$ref": "#/components/schemas/Modal"}}}}}}, "400": {"description": "Invalid request or modal not found"}}}}, "/private/modal/deleteModal": {"post": {"summary": "Delete a modal", "tags": ["Modal"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "description": "Modal ID to delete"}}}}}}, "responses": {"200": {"description": "<PERSON>dal deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}}}}, "400": {"description": "Invalid request or modal not found"}, "500": {"description": "Permission denied or server error"}}}}, "/private/modal/updateModal": {"post": {"summary": "Update modal details", "tags": ["Modal"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "string", "description": "Modal ID"}, "name": {"type": "string", "description": "New modal name"}, "type": {"type": "string", "description": "New modal type"}, "content": {"type": "object", "description": "Updated modal content"}}}}}}, "responses": {"200": {"description": "Modal updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}}}}, "400": {"description": "Invalid request or modal not found"}, "500": {"description": "Permission denied or server error"}}}}, "/private/modal/getAllModal": {"post": {"summary": "Get all modals with pagination and filtering", "tags": ["Modal"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"page": {"type": "number", "description": "Page number"}, "size": {"type": "number", "description": "Items per page"}, "keyword": {"type": "string", "description": "Search keyword"}, "status": {"type": "string", "enum": ["active", "inactive"], "description": "Filter by status"}}}}}}, "responses": {"200": {"description": "Modals fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "modal": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/components/schemas/Modal"}}, "count": {"type": "number"}}}}}}}}, "400": {"description": "Invalid request"}, "500": {"description": "Permission denied or server error"}}}}, "/private/modal/updateModalStatus": {"post": {"summary": "Update modal status", "tags": ["Modal"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id", "status"], "properties": {"id": {"type": "string", "description": "Modal ID"}, "status": {"type": "string", "enum": ["active", "inactive"], "description": "New status"}}}}}}, "responses": {"200": {"description": "Modal status updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}}}}, "400": {"description": "Invalid request or modal not found"}, "500": {"description": "Permission denied or server error"}}}}, "/private/modal/insertModalAnswer": {"post": {"summary": "Insert answers for a modal", "tags": ["Modal"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ModalAnswer"}}}}, "responses": {"200": {"description": "Modal answer inserted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}}}}, "400": {"description": "Invalid request"}, "500": {"description": "Permission denied or server error"}}}}, "/private/modal/getModalType": {"get": {"summary": "Get all modal types", "tags": ["Modal"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Modal types fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "modal_type": {"type": "array", "items": {"$ref": "#/components/schemas/ModalType"}}}}}}}, "400": {"description": "Invalid request"}}}}, "/private/modal/addModalType": {"post": {"summary": "Add a new modal type", "tags": ["Modal"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "Name of the modal type"}}}}}}, "responses": {"200": {"description": "Modal type added successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "modalId": {"type": "string"}}}}}}, "400": {"description": "Invalid request"}, "500": {"description": "Permission denied or server error"}}}}, "/private/modal/deleteModalType": {"post": {"summary": "Delete a modal type", "tags": ["Modal"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "string", "description": "Modal type ID to delete"}}}}}}, "responses": {"200": {"description": "Modal type deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}}}}, "400": {"description": "Invalid request"}, "500": {"description": "Permission denied or server error"}}}}, "/private/noti/getListNoti": {"get": {"summary": "Get paginated list of notifications", "tags": ["Notifications"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "user_id", "required": true, "schema": {"type": "string"}, "description": "ID of user to get notifications for"}, {"in": "query", "name": "page", "schema": {"type": "integer", "minimum": 1}, "description": "Page number for pagination"}, {"in": "query", "name": "size", "schema": {"type": "integer", "minimum": 1}, "description": "Number of items per page"}], "responses": {"200": {"description": "List of notifications retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Get list noti successfully"}, "data": {"type": "array", "items": {"type": "object"}}}}}}}}}}, "/private/noti/getCountNoti": {"get": {"summary": "Get count of unread notifications", "tags": ["Notifications"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "user_id", "required": true, "schema": {"type": "string"}, "description": "ID of user to get notification count for"}], "responses": {"200": {"description": "Notification count retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Get count noti successfully"}, "data": {"type": "integer", "example": 5}}}}}}}}}, "/private/noti/getNotiById": {"get": {"summary": "Get notification by ID", "tags": ["Notifications"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "noti_id", "required": true, "schema": {"type": "string"}, "description": "ID of notification to retrieve"}], "responses": {"200": {"description": "Notification retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Get noti successfully"}, "data": {"type": "object"}}}}}}}}}, "/private/noti/updateStatusNoti": {"post": {"summary": "Update notification status", "tags": ["Notifications"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["noti_id", "status"], "properties": {"noti_id": {"type": "string", "description": "ID of notification to update"}, "status": {"type": "integer", "enum": [1, 2, 3], "description": "New status for notification"}}}}}}, "responses": {"200": {"description": "Notification status updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Update status seen noti successfully"}}}}}}, "500": {"description": "Invalid status value", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Status invalid"}}}}}}}}}, "/private/noti/updateStatusMultiNoti": {"post": {"summary": "Update status for multiple notifications", "tags": ["Notifications"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["noti_id", "status"], "properties": {"noti_id": {"type": "array", "items": {"type": "string"}, "description": "Array of notification IDs to update"}, "status": {"type": "integer", "enum": [1, 2, 3], "description": "New status for notifications"}}}}}}, "responses": {"200": {"description": "Notifications status updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Update status seen noti successfully"}}}}}}, "500": {"description": "Invalid status value", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Status invalid"}}}}}}}}}, "/private/noti/updateStatusSeenAllNoti": {"post": {"summary": "Mark all notifications as seen for a user", "tags": ["Notifications"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["user_id"], "properties": {"user_id": {"type": "string", "description": "ID of user whose notifications to mark as seen"}}}}}}, "responses": {"200": {"description": "All notifications marked as seen", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Update status seen all noti successfully"}}}}}}}}}, "/private/noti/deleteNotification": {"post": {"summary": "Delete a notification", "tags": ["Notifications"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["noti_id"], "properties": {"noti_id": {"type": "string", "description": "ID of notification to delete"}}}}}}, "responses": {"200": {"description": "Notification deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Delete noti successfully"}}}}}}, "500": {"description": "Failed to delete notification", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Delete noti failed"}}}}}}}}}, "/private/questionaire/createQuestionaire": {"post": {"summary": "Create a new questionnaire", "tags": ["Questionnaire"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["qtn_name", "tem_id", "lf_id", "price"], "properties": {"qtn_name": {"type": "string"}, "tem_id": {"type": "integer"}, "lf_id": {"type": "integer"}, "price": {"type": "number"}}}}}}, "responses": {"200": {"description": "Questionnaire created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Questionnaire created"}, "questionaire_id": {"type": "integer"}}}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/private/questionaire/getAllQuestionaire": {"post": {"summary": "Get all questionnaires with pagination", "tags": ["Questionnaire"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["page", "size"], "properties": {"page": {"type": "integer", "description": "Page number"}, "size": {"type": "integer", "description": "Items per page"}, "keyword": {"type": "string", "description": "Search keyword"}, "status": {"type": "integer", "description": "Filter by status"}, "lf_id": {"type": "integer", "description": "Law firm ID (required for Admin role only)"}}}}}}, "responses": {"200": {"description": "List of questionnaires", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "ok"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Questionnaire"}}, "total": {"type": "integer"}}}}}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Permission denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/private/questionaire/getQuestionaire": {"post": {"summary": "Get a specific questionnaire by ID", "tags": ["Questionnaire"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "integer", "description": "Questionnaire ID"}}}}}}, "responses": {"200": {"description": "Questionnaire details", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Questionnaire fetched"}, "questionaire": {"$ref": "#/components/schemas/Questionnaire"}}}}}}, "400": {"description": "Invalid request or questionnaire not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/private/questionaire/deleteQuestionaire": {"post": {"summary": "Delete a questionnaire (Admin only)", "tags": ["Questionnaire"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "integer", "description": "Questionnaire ID to delete"}}}}}}, "responses": {"200": {"description": "Questionnaire deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Questionnaire deleted"}}}}}}, "400": {"description": "Invalid request or questionnaire not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Permission denied (non-admin users)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/private/questionaire/updateQuestionaire": {"post": {"summary": "Update a questionnaire (Admin or LawfirmSuperAdmin only)", "tags": ["Questionnaire"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "integer", "description": "Questionnaire ID to update"}, "qtn_name": {"type": "string", "description": "New questionnaire name"}, "tem_id": {"type": "integer", "description": "New template ID"}, "price": {"type": "number", "description": "New price"}}}}}}, "responses": {"200": {"description": "Questionnaire updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Questionnaire updated"}}}}}}, "400": {"description": "Invalid request or questionnaire not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Permission denied (insufficient role)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/private/questionaire/updateQuestionaireStatus": {"post": {"summary": "Update questionnaire status (Admin or LawfirmSuperAdmin only)", "tags": ["Questionnaire"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id", "status"], "properties": {"id": {"type": "integer", "description": "Questionnaire ID"}, "status": {"type": "integer", "description": "New status (0=draft, 1=active, 2=inactive, 3=pending)"}}}}}}, "responses": {"200": {"description": "Status updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Questionnaire updated"}}}}}}, "400": {"description": "Invalid request or questionnaire not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Permission denied (insufficient role)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/private/questionaire/requestUpdateQuestionaire": {"post": {"summary": "Request to update a questionnaire (LawfirmAdmin or LawfirmUser only)", "tags": ["Questionnaire"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["qtn_id"], "properties": {"qtn_id": {"type": "integer", "description": "Questionnaire ID to request update for"}}}}}}, "responses": {"200": {"description": "Update request submitted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Questionnaire update requested"}}}}}}, "400": {"description": "Invalid request, questionnaire not found, or already has pending request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Permission denied (wrong role or law firm)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/private/questionaire/createLink": {"post": {"summary": "Create a link for a questionnaire (Admin or LawfirmSuperAdmin only)", "tags": ["Questionnaire"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["questionaire_id"], "properties": {"questionaire_id": {"type": "integer", "description": "ID of the questionnaire to create link for"}}}}}}, "responses": {"200": {"description": "Link created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Link created"}, "questionnaire": {"type": "object"}}}}}}, "400": {"description": "Invalid request or questionnaire not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Permission denied (insufficient role)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/private/questionaire/findRequest": {"post": {"summary": "Find questionnaire requests by status (Non-Client users only)", "tags": ["Questionnaire"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["qtn_id", "status"], "properties": {"qtn_id": {"type": "integer", "description": "Questionnaire ID"}, "status": {"type": "integer", "description": "Request status to filter by"}}}}}}, "responses": {"200": {"description": "Request details fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Request fetched"}, "request": {"type": "object"}}}}}}, "400": {"description": "Invalid request or questionnaire not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Permission denied (Client role or wrong law firm)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/private/questionaire/modifyQuestion": {"post": {"summary": "Modify questions in a questionnaire (Admin or LawfirmSuperAdmin only)", "tags": ["Questionnaire"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["questionnaire_id", "questions"], "properties": {"questionnaire_id": {"type": "integer", "description": "ID of the questionnaire to modify"}, "questions": {"type": "array", "description": "Array of modified questions", "items": {"type": "object"}}}}}}}, "responses": {"200": {"description": "Questions modified successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Question Edited"}, "questionnaire": {"type": "object"}}}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Permission denied (insufficient role)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/private/questionaire/getCompletedRequests": {"post": {"summary": "Get completed requests for a questionnaire", "tags": ["Questionnaire"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "integer", "description": "Questionnaire ID"}}}}}}, "responses": {"200": {"description": "Completed requests fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Requests fetched"}, "requests": {"type": "array", "items": {"type": "object"}}}}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/private/questionaire/getAllQuestionaireForPMS": {"post": {"summary": "Get questionnaires for PMS with pagination", "tags": ["Questionnaire"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["page", "size"], "properties": {"page": {"type": "integer", "description": "Page number"}, "size": {"type": "integer", "description": "Items per page"}, "keyword": {"type": "string", "description": "Search keyword"}, "status": {"type": "integer", "description": "Filter by status"}}}}}}, "responses": {"200": {"description": "List of questionnaires for PMS", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "ok"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Questionnaire"}}, "total": {"type": "integer"}}}}}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/private/sms/sendLinkCaseVerify": {"post": {"summary": "Send case verification link via SMS", "tags": ["SMS"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaseVerifyRequest"}}}}, "responses": {"200": {"description": "<PERSON> sent successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "link": {"type": "string", "description": "Generated verification link"}}}}}}, "500": {"description": "Permission denied or server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}}}}}}}, "/private/sms/sendOTP": {"post": {"summary": "Send OTP code via SMS for login", "tags": ["SMS"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OTPRequest"}}}}, "responses": {"200": {"description": "OTP sent successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "result": {"type": "object", "description": "OTP details (only in development)"}}}}}}, "500": {"description": "Error sending O<PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}}}}}}}, "/private/sms/verifyOTP": {"post": {"summary": "Verify OTP code sent via SMS", "tags": ["SMS"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OTPVerifyRequest"}}}}, "responses": {"200": {"description": "OTP verified successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "result": {"type": "object", "description": "Verification result"}}}}}}, "500": {"description": "Error verifying OTP", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}}}}}}}, "/private/template/createTemplate": {"post": {"summary": "Create a new template", "tags": ["Template"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["tem_name"], "properties": {"tem_name": {"type": "string"}, "tem_desc": {"type": "string"}}}}}}, "responses": {"200": {"description": "Template created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "template": {"$ref": "#/components/schemas/Template"}}}}}}, "400": {"description": "Invalid request"}, "500": {"description": "Permission denied or server error"}}}}, "/private/template/getTemplate": {"post": {"summary": "Get template by ID", "tags": ["Template"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "string"}}}}}}, "responses": {"200": {"description": "Template fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "template": {"$ref": "#/components/schemas/Template"}}}}}}, "400": {"description": "Invalid request"}, "500": {"description": "Permission denied or server error"}}}}, "/private/template/deleteTemplate": {"post": {"summary": "Delete a template", "tags": ["Template"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "string"}}}}}}, "responses": {"200": {"description": "Template deleted successfully"}, "400": {"description": "Invalid request"}, "500": {"description": "Permission denied or server error"}}}}, "/private/template/updateTemplate": {"post": {"summary": "Update template details", "tags": ["Template"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "string"}, "tem_name": {"type": "string"}, "tem_desc": {"type": "string"}}}}}}, "responses": {"200": {"description": "Template updated successfully"}, "400": {"description": "Invalid request or template not found"}, "500": {"description": "Permission denied or server error"}}}}, "/private/template/updateTemplateStatus": {"post": {"summary": "Update template status", "tags": ["Template"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id", "status"], "properties": {"id": {"type": "string"}, "status": {"type": "string", "enum": ["active", "inactive"]}}}}}}, "responses": {"200": {"description": "Template status updated successfully"}, "400": {"description": "Invalid request or template not found"}, "500": {"description": "Permission denied or server error"}}}}, "/private/template/createTemplateFromJson": {"post": {"summary": "Create a template from JSON data", "tags": ["Template"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"template": {"$ref": "#/components/schemas/Template"}}}}}}, "responses": {"200": {"description": "Template created successfully"}, "400": {"description": "Invalid request"}, "500": {"description": "Permission denied or server error"}}}}, "/private/template/getAllTemplate": {"post": {"summary": "Get all templates with pagination", "tags": ["Template"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginationRequest"}}}}, "responses": {"200": {"description": "Templates fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "template": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/components/schemas/Template"}}, "count": {"type": "number"}}}}}}}}, "500": {"description": "Permission denied or server error"}}}}, "/private/template/createTemplateAnswer": {"post": {"summary": "Create a template answer", "tags": ["Template"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateAnswer"}}}}, "responses": {"200": {"description": "Template answer created successfully"}, "400": {"description": "Invalid request"}, "500": {"description": "Permission denied or server error"}}}}, "/private/template/getTemplateAnswer": {"post": {"summary": "Get template answer by ID", "tags": ["Template"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "string"}}}}}}, "responses": {"200": {"description": "Template answer fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "template": {"$ref": "#/components/schemas/TemplateAnswer"}}}}}}, "400": {"description": "Invalid request"}, "500": {"description": "Permission denied or server error"}}}}, "/private/template/deleteTemplateAnswer": {"post": {"summary": "Delete a template answer", "tags": ["Template"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "string"}}}}}}, "responses": {"200": {"description": "Template answer deleted successfully"}, "400": {"description": "Invalid request"}, "500": {"description": "Permission denied or server error"}}}}, "/private/template/updateTemplateAnswer": {"post": {"summary": "Update template answer", "tags": ["Template"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "string"}, "answers": {"type": "object"}}}}}}, "responses": {"200": {"description": "Template answer updated successfully"}, "400": {"description": "Invalid request or answer not found"}, "500": {"description": "Permission denied or server error"}}}}, "/private/template/getAllTemplateAnswer": {"post": {"summary": "Get all template answers with pagination", "tags": ["Template"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PaginationRequest"}, {"type": "object", "properties": {"status": {"type": "string", "enum": ["active", "inactive"]}}}]}}}}, "responses": {"200": {"description": "Template answers fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "template": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/components/schemas/TemplateAnswer"}}, "count": {"type": "number"}}}}}}}}, "500": {"description": "Permission denied or server error"}}}}, "/private/template/updateTemplateAnswerStatus": {"post": {"summary": "Update template answer status", "tags": ["Template"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id", "status"], "properties": {"id": {"type": "string"}, "status": {"type": "string", "enum": ["active", "inactive"]}}}}}}, "responses": {"200": {"description": "Template answer status updated successfully"}, "400": {"description": "Invalid request or answer not found"}, "500": {"description": "Permission denied or server error"}}}}, "/private/template/createLink": {"post": {"summary": "Create a link for a template", "tags": ["Template"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "string", "description": "Template ID to create link for"}}}}}}, "responses": {"200": {"description": "Link created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "template": {"type": "object"}}}}}}, "400": {"description": "Invalid request"}, "500": {"description": "Permission denied or server error"}}}}, "/private/template/modifyQuestion": {"post": {"summary": "Modify a question in a template", "tags": ["Template"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id", "question"], "properties": {"id": {"type": "string", "description": "Template ID"}, "question": {"type": "object", "description": "Modified question data"}}}}}}, "responses": {"200": {"description": "Question modified successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "template": {"type": "object"}}}}}}, "400": {"description": "Invalid request"}, "500": {"description": "Permission denied or server error"}}}}, "/private/user/findUser": {"get": {"summary": "Find user by email", "tags": ["Users"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "email", "schema": {"type": "string"}, "required": true, "description": "User's email address"}], "responses": {"200": {"description": "User found successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/User"}}}}}}, "500": {"description": "Error finding user or permission denied"}}}}, "/private/user/getUser": {"get": {"summary": "Get current user information", "tags": ["Users"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "User information retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/User"}}}}}}}}}, "/private/user/deleteUser": {"post": {"summary": "Delete a user", "tags": ["Users"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_id": {"type": "string"}}}}}}, "responses": {"200": {"description": "User deleted successfully"}, "500": {"description": "Error deleting user or permission denied"}}}}, "/private/user/updateUser": {"post": {"summary": "Update user information", "tags": ["Users"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"new_email": {"type": "string"}, "title": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "mb_phone": {"type": "string"}}}}}}, "responses": {"200": {"description": "User updated successfully"}, "500": {"description": "Error updating user or validation failed"}}}}, "/private/user/addLFUser": {"post": {"summary": "Add a user to a law firm", "tags": ["Law Firms"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string"}, "role": {"type": "string"}, "lf_name": {"type": "string"}}}}}}, "responses": {"200": {"description": "User added to law firm successfully"}, "500": {"description": "Error adding user or permission denied"}}}}, "/private/user/updateRole": {"post": {"summary": "Update user role", "tags": ["Users"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["user_id", "role"], "properties": {"user_id": {"type": "string"}, "role": {"type": "string", "description": "New role for the user"}}}}}}, "responses": {"200": {"description": "User role updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/User"}}}}}}, "500": {"description": "Error updating user role"}}}}, "/private/user/getLawfirm": {"get": {"summary": "Get law firm information", "tags": ["Law Firms"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Law firm information retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"lf_id": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "integer"}, "mandate": {"type": "string"}, "prefix": {"type": "string"}}}}}}}}, "500": {"description": "Error retrieving law firm information"}}}}, "/private/user/getUserLawfirm": {"get": {"summary": "Get law firm information for current user", "tags": ["Law Firms"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "User's law firm information retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"lf_id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "status": {"type": "integer"}}}}}}}}, "500": {"description": "Error retrieving law firm information"}}}}, "/private/user/addUser": {"post": {"summary": "Add a new user", "tags": ["Users"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "role", "lf_id"], "properties": {"email": {"type": "string"}, "role": {"type": "string"}, "lf_id": {"type": "string"}, "title": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "mb_phone": {"type": "string"}}}}}}, "responses": {"200": {"description": "User added successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/User"}}}}}}, "500": {"description": "Error adding user or validation failed"}}}}, "/private/user/updateLawfirm": {"post": {"summary": "Update law firm information", "tags": ["Law Firms"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["lf_id"], "properties": {"lf_id": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "integer"}, "mandate": {"type": "string"}, "prefix": {"type": "string"}}}}}}, "responses": {"200": {"description": "Law firm updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"lf_id": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "integer"}, "mandate": {"type": "string"}, "prefix": {"type": "string"}}}}}}}}, "500": {"description": "Error updating law firm"}}}}, "/private/user/findUserById": {"get": {"summary": "Find user by ID", "tags": ["Users"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "user_id", "schema": {"type": "string"}, "required": true, "description": "User ID to search for"}], "responses": {"200": {"description": "User found successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/User"}}}}}}, "500": {"description": "Error finding user or permission denied"}}}}, "/private/user/getLawfirmDetails": {"post": {"summary": "Get detailed law firm information", "tags": ["Law Firms"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["lf_id"], "properties": {"lf_id": {"type": "string"}}}}}}, "responses": {"200": {"description": "Law firm details retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"lf_id": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "integer"}, "mandate": {"type": "string"}, "prefix": {"type": "string"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}}}}}}}, "500": {"description": "Error retrieving law firm details"}}}}, "/private/user/getLawfirmDetailsWithGraph": {"post": {"summary": "Get detailed law firm information with Graph credentials status", "tags": ["Law Firms"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["lf_id"], "properties": {"lf_id": {"type": "integer", "description": "Law firm ID"}}}}}}, "responses": {"200": {"description": "Law firm details with Graph credentials status retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"lf_id": {"type": "integer"}, "lf_org_name": {"type": "string"}, "graphCredentials": {"type": "object", "nullable": true, "properties": {"id": {"type": "integer"}, "tenant_id": {"type": "string"}, "client_id": {"type": "string"}, "is_active": {"type": "boolean"}}}, "mailProvider": {"type": "object", "properties": {"primaryProvider": {"type": "string"}, "fallbackProvider": {"type": "string"}, "graphConfigured": {"type": "boolean"}}}}}}}}}}, "500": {"description": "Error retrieving law firm details or permission denied"}}}}, "/private/user/updateLawFirmStatus": {"post": {"summary": "Update law firm status", "tags": ["Law Firms"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["lf_id", "status"], "properties": {"lf_id": {"type": "string"}, "status": {"type": "integer", "description": "New status code for the law firm"}}}}}}, "responses": {"200": {"description": "Law firm status updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"lf_id": {"type": "string"}, "status": {"type": "integer"}}}}}}}}, "500": {"description": "Error updating law firm status"}}}}, "/private/user/updateLawFirmMandate": {"post": {"summary": "Update law firm mandate", "tags": ["Law Firms"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["lf_id", "mandate"], "properties": {"lf_id": {"type": "string"}, "mandate": {"type": "string", "description": "New mandate for the law firm"}}}}}}, "responses": {"200": {"description": "Law firm mandate updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"lf_id": {"type": "string"}, "mandate": {"type": "string"}}}}}}}}, "500": {"description": "Error updating law firm mandate"}}}}, "/private/user/updateLawFirmPrefix": {"post": {"summary": "Update law firm prefix", "tags": ["Law Firms"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["lf_id", "prefix"], "properties": {"lf_id": {"type": "string"}, "prefix": {"type": "string", "description": "New prefix for the law firm"}}}}}}, "responses": {"200": {"description": "Law firm prefix updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"lf_id": {"type": "string"}, "prefix": {"type": "string"}}}}}}}}, "500": {"description": "Error updating law firm prefix"}}}}, "/private/user/updateUserStatus": {"post": {"summary": "Update user status", "tags": ["Users"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["user_id", "status"], "properties": {"user_id": {"type": "string"}, "status": {"type": "integer", "description": "New status code for the user"}}}}}}, "responses": {"200": {"description": "User status updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/User"}}}}}}, "500": {"description": "Error updating user status"}}}}, "/private/user/getClientLawfirm": {"get": {"summary": "Get client law firm information", "tags": ["Law Firms"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Client law firm information retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"lf_id": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "integer"}, "mandate": {"type": "string"}, "prefix": {"type": "string"}}}}}}}}, "500": {"description": "Error retrieving client law firm information"}}}}, "/private/user/changePassword": {"post": {"summary": "Change user password", "tags": ["Authentication"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["current_password", "new_password"], "properties": {"current_password": {"type": "string", "description": "User's current password"}, "new_password": {"type": "string", "description": "New password to set"}}}}}}, "responses": {"200": {"description": "Password changed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}}}}, "400": {"description": "Invalid current password or weak new password"}, "500": {"description": "Error changing password"}}}}, "/private/user/resetPassword": {"post": {"summary": "Reset user password", "tags": ["Authentication"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "new_password"], "properties": {"email": {"type": "string", "description": "User's email address"}, "new_password": {"type": "string", "description": "New password to set"}}}}}}, "responses": {"200": {"description": "Password reset successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}}}}, "400": {"description": "Invalid email or weak new password"}, "500": {"description": "Error resetting password"}}}}, "/private/user/uploadBanner": {"post": {"summary": "Upload a banner image", "tags": ["Users"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"banner": {"type": "string", "format": "binary", "description": "Banner image file to upload"}}}}}}, "responses": {"200": {"description": "<PERSON> uploaded successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "url": {"type": "string", "description": "URL of the uploaded banner"}}}}}}, "400": {"description": "Invalid file type or size"}, "500": {"description": "Error uploading banner"}}}}, "/private/user/logout": {"post": {"summary": "Log out current user", "tags": ["Authentication"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "User logged out successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}}}}, "500": {"description": "Error logging out"}}}}, "/private/user/getListAdmin": {"get": {"summary": "Get list of admin users", "tags": ["Users"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "List of admin users retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}}}}, "500": {"description": "Error retrieving admin list"}}}}, "/private/user/resendInvitation": {"post": {"summary": "Resend invitation email to user", "tags": ["Users"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string"}}}}}}, "responses": {"200": {"description": "Invitation resent successfully"}}}}, "/private/user/saveClientFromPMS": {"post": {"summary": "Save client information from PMS", "tags": ["Users"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["client_data"], "properties": {"client_data": {"type": "object", "properties": {"email": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}}}}}}}}, "responses": {"200": {"description": "Client saved successfully from PMS"}}}}, "/private/user/createKeyForLawFirm": {"post": {"summary": "Create API key for law firm", "tags": ["Law Firms"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["lf_id"], "properties": {"lf_id": {"type": "string"}}}}}}, "responses": {"200": {"description": "API key created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "api_key": {"type": "string"}}}}}}}}}, "/private/user/regenKeyForLawFirm": {"post": {"summary": "Regenerate API key for law firm", "tags": ["Law Firms"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["lf_id"], "properties": {"lf_id": {"type": "string"}}}}}}, "responses": {"200": {"description": "API key regenerated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "api_key": {"type": "string"}}}}}}}}}, "/private/user/updateProfileAdmin": {"post": {"summary": "Update admin profile", "tags": ["Users"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"first_name": {"type": "string"}, "last_name": {"type": "string"}, "email": {"type": "string"}}}}}}, "responses": {"200": {"description": "Admin profile updated successfully"}}}}, "/private/user/updateConnectPMS": {"post": {"summary": "Update PMS connection settings", "tags": ["Law Firms"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["lf_id", "pms_settings"], "properties": {"lf_id": {"type": "string"}, "pms_settings": {"type": "object"}}}}}}, "responses": {"200": {"description": "PMS connection settings updated successfully"}}}}, "/public/base/generateJWT": {"post": {"summary": "Generate JWT token", "tags": ["Authentication"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["user_id", "role"], "properties": {"user_id": {"type": "string", "description": "User ID for token generation"}, "role": {"type": "string", "description": "User role for access control"}}}}}}, "responses": {"200": {"description": "JWT token generated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "token": {"type": "string", "description": "Generated JWT token"}}}}}}, "400": {"description": "Invalid user ID or role"}, "500": {"description": "Error generating JWT token"}}}}, "/public/case/getPrefixFromCaseId": {"get": {"summary": "Get law firm prefix from case ID", "tags": ["Cases"], "parameters": [{"in": "query", "name": "case_id", "schema": {"type": "string"}, "required": true, "description": "Case ID to get prefix for"}], "responses": {"200": {"description": "Prefix retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "prefix": {"type": "string", "description": "Law firm prefix"}}}}}}, "404": {"description": "Case not found"}, "500": {"description": "Error retrieving prefix"}}}}, "/public/customMail/getStatement": {"get": {"summary": "Get statement document", "tags": ["Mail"], "parameters": [{"in": "query", "name": "statement_id", "schema": {"type": "string"}, "required": true, "description": "Statement ID to retrieve"}], "responses": {"200": {"description": "Statement retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "statement": {"type": "object", "description": "Statement document"}}}}}}, "404": {"description": "Statement not found"}, "500": {"description": "Error retrieving statement"}}}}, "/public/mail/sendOTP": {"post": {"summary": "Send OTP to user's email", "tags": ["Authentication"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string", "description": "User's email address"}}}}}}, "responses": {"200": {"description": "OTP sent successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}}}}, "400": {"description": "Invalid email format"}, "500": {"description": "Error sending O<PERSON>"}}}}, "/public/mail/verifyOTP": {"post": {"summary": "Verify OTP code", "tags": ["Authentication"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "otp"], "properties": {"email": {"type": "string", "description": "User's email address"}, "otp": {"type": "string", "description": "OTP code to verify"}}}}}}, "responses": {"200": {"description": "OTP verified successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}}}}, "400": {"description": "Invalid OTP or expired"}, "500": {"description": "Error verifying OTP"}}}}, "/public/sms/sendSMS": {"post": {"summary": "Send OTP via SMS", "tags": ["Authentication"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["phone_number"], "properties": {"phone_number": {"type": "string", "description": "User's phone number"}}}}}}, "responses": {"200": {"description": "SMS OTP sent successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}}}}, "400": {"description": "Invalid phone number format"}, "500": {"description": "Error sending SMS"}}}}, "/public/sms/verifySMS": {"post": {"summary": "Verify SMS OTP code", "tags": ["Authentication"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["phone_number", "otp"], "properties": {"phone_number": {"type": "string", "description": "User's phone number"}, "otp": {"type": "string", "description": "OTP code to verify"}}}}}}, "responses": {"200": {"description": "SMS OTP verified successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}}}}, "400": {"description": "Invalid OTP or expired"}, "500": {"description": "Error verifying SMS OTP"}}}}, "/public/user/login": {"post": {"summary": "User login", "tags": ["Authentication"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "description": "User's email address"}, "password": {"type": "string", "description": "User's password"}}}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "token": {"type": "string", "description": "JWT authentication token"}}}}}}, "401": {"description": "Invalid credentials"}, "500": {"description": "Error during login"}}}}, "/public/user/getLawfirm": {"get": {"summary": "Get law firm information", "tags": ["Law Firms"], "responses": {"200": {"description": "Law firm information retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"lf_id": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "integer"}, "mandate": {"type": "string"}, "prefix": {"type": "string"}}}}}}}}, "500": {"description": "Error retrieving law firm information"}}}}, "/public/user/refreshToken": {"post": {"summary": "Refresh authentication token", "tags": ["Authentication"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["refreshToken"], "properties": {"refreshToken": {"type": "string", "description": "Refresh token to generate new access token"}}}}}}, "responses": {"200": {"description": "<PERSON><PERSON> refreshed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "token": {"type": "string", "description": "New JWT authentication token"}}}}}}, "401": {"description": "Invalid refresh token"}, "500": {"description": "Error refreshing token"}}}}, "/public/user/getLawfirmSettings": {"get": {"summary": "Get law firm settings", "tags": ["Law Firms"], "responses": {"200": {"description": "Law firm settings retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"settings": {"type": "object", "description": "Law firm settings object"}}}}}}}}, "500": {"description": "Error retrieving law firm settings"}}}}, "/public/user/tokenForAddClientPMS": {"post": {"summary": "Generate token for adding client from PMS", "tags": ["Law Firms"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["lf_id"], "properties": {"lf_id": {"type": "string", "description": "Law firm ID"}}}}}}, "responses": {"200": {"description": "Token generated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "token": {"type": "string", "description": "Generated token for PMS integration"}}}}}}, "500": {"description": "Error generating token"}}}}, "/api/firm/login": {"post": {"summary": "Two-layer authentication - API token + user credentials", "description": "Requires both API token (for firm authentication) and email/password (for user authentication)", "tags": ["Firm API"], "security": [{"apiTokenAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "description": "User's email address", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "description": "User's password", "example": "userpassword123"}}}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Login successful"}, "data": {"type": "object", "properties": {"access_token": {"type": "string", "description": "JWT token for the authenticated user"}, "user": {"type": "object", "properties": {"user_id": {"type": "integer"}, "email": {"type": "string"}, "role": {"type": "string"}, "lf_id": {"type": "integer"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}}}}}}}}}}, "400": {"description": "Missing email or password"}, "401": {"description": "Invalid email or password"}, "403": {"description": "User does not belong to authorized law firm"}}}}, "/api/firm/users": {"post": {"summary": "Add user to law firm", "tags": ["Firm API"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "first_name", "last_name", "role"], "properties": {"email": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "role": {"type": "string", "enum": ["2", "3", "4"], "description": "2=LawfirmAdmin, 3=LawfirmUser, 4=Client"}, "org_type": {"type": "string"}, "org_name": {"type": "string"}, "title": {"type": "string"}, "middle_name": {"type": "string"}, "dob": {"type": "string", "format": "date"}, "gender": {"type": "string"}, "home_phone": {"type": "string"}, "mb_phone": {"type": "string"}, "wrk_phone": {"type": "string"}, "adr_1": {"type": "string"}, "adr_2": {"type": "string"}, "adr_3": {"type": "string"}, "state": {"type": "string"}, "town": {"type": "string"}, "country": {"type": "string"}, "post_code": {"type": "string"}, "pms_id": {"type": "string"}}}}}}, "responses": {"201": {"description": "User added successfully"}}}}, "/api/firm/cases": {"get": {"summary": "Get cases for law firm", "tags": ["Firm API"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}}, {"in": "query", "name": "size", "schema": {"type": "integer", "default": 10}}, {"in": "query", "name": "status", "schema": {"type": "string"}}, {"in": "query", "name": "user_id", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Cases retrieved successfully"}}}}, "/api/firm/case": {"post": {"summary": "Get specific case details", "tags": ["Firm API"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["case_id"], "properties": {"case_id": {"type": "string", "description": "Case ID to retrieve"}}}}}}, "responses": {"200": {"description": "Case details retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"case": {"type": "object", "description": "Case information"}, "user": {"type": "object", "description": "User information associated with the case"}}}}}}}}, "400": {"description": "Missing case_id or invalid request"}, "403": {"description": "Case does not belong to your law firm"}, "404": {"description": "Case not found"}}}}, "/api/firm/questionnaire": {"post": {"summary": "Get specific questionnaire details", "tags": ["Firm API"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["qtn_id"], "properties": {"qtn_id": {"type": "string", "description": "Questionnaire ID to retrieve"}}}}}}, "responses": {"200": {"description": "Questionnaire details retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "description": "Questionnaire information"}}}}}}, "400": {"description": "Missing qtn_id or invalid request"}, "403": {"description": "Questionnaire does not belong to your law firm"}, "404": {"description": "Questionnaire not found"}}}}, "/api/firm/cases/update": {"post": {"summary": "Update a case (matches normal implementation)", "tags": ["Firm API"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "string", "description": "Case ID"}, "status": {"type": "integer", "description": "New case status"}, "case_name": {"type": "string", "description": "Case name"}, "desc": {"type": "string", "description": "Case description"}}}}}}, "responses": {"200": {"description": "Case updated successfully"}}}}, "/api/firm/cases/export": {"post": {"summary": "Export case data (matches normal implementation)", "tags": ["Firm API"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["case_id"], "properties": {"case_id": {"type": "string", "description": "Case ID"}, "format": {"type": "string", "enum": ["json", "csv", "excel"], "default": "json", "description": "Export format"}, "check": {"type": "integer", "enum": [1, 2], "default": 1, "description": "Excel spreadsheet type (only for excel format)"}}}}}}, "responses": {"200": {"description": "Case data exported successfully"}}}}, "/api/firm/lawfirm": {"get": {"summary": "Get law firm information (for debugging)", "tags": ["Firm API"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Law firm information retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"lf_id": {"type": "integer"}, "lf_org_name": {"type": "string"}, "prefix": {"type": "string"}, "status": {"type": "integer"}}}}}}}}, "404": {"description": "Law firm not found"}}}}}, "tags": [{"name": "Billing", "description": "Billing management endpoints"}, {"name": "Cases", "description": "Case management endpoints"}, {"name": "Mail", "description": "Email management endpoints"}, {"name": "Notifications", "description": "Notification management endpoints"}, {"name": "Users", "description": "User management endpoints"}, {"name": "Law Firms", "description": "Law firm management endpoints"}, {"name": "Authentication", "description": "Authentication related endpoints"}, {"name": "Firm API", "description": "Law firm-level API operations with two-step authentication:\n1. Use API token to login via /api/firm/login\n2. Use returned JWT token for all other operations\n"}]}