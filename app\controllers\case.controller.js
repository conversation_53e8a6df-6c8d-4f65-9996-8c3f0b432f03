import * as fs from "fs";
import { caseService } from "#services";
import { catchAsync } from "#utils";
import { roles } from "#middlewares/roles.js";
import { smsService } from "#services";
import axios from "axios";
import validator from "validator";
import FormData from "form-data";
import { filter_pms } from "#middlewares/filter_pms.js";
import { default as baseLogger } from "#logger";
import { checkConnectPMS } from "../services/case.service.js";
import { fileURLToPath } from "url";
import path from "path";
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const logger = baseLogger.getLogger(`import.meta.url: ${import.meta.url}`);

export const createCase = catchAsync(async (req, res) => {
  try {
    const assigned_by = req.user.user_id;
    const { case_name, cases, user_id, qtn_id, case_id_pms, desc, token_ } =
      req.body;

    let case_ = await caseService.createCase(
      case_name,
      desc,
      cases,
      qtn_id,
      user_id,
      assigned_by,
      case_id_pms,
      token_
    );
    if (case_.check == 1) {
      let userInfo = await smsService.getInfo(case_.case_id);
      let result = smsService.sendLinkCaseVerify(
        userInfo.first_name,
        userInfo.email,
        userInfo.phone.slice(-4),
        case_.case_id,
        req.user.lf_id,
        userInfo.user_id,
        req.user
      );
    } else if (case_.check == 2) {
      let userInfo = await smsService.getInfo(case_.case_id);
      let result = smsService.sendChaserMail(
        userInfo.first_name,
        userInfo.email,
        userInfo.phone.slice(-4),
        case_.case_id,
        req.user.lf_id,
        userInfo.user_id,
        req.user
      );
    }
    return res.status(200).json({
      success: true,
      message: "Case created",
      case_id: case_.case_id,
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message,
    });
  }
});

export const getCase = catchAsync(async (req, res) => {
  const { id } = req.body;
  const user_access = await caseService.getAllGranted(id);
  if (
    req.user.role == roles.LawfirmSuperAdmin ||
    req.user.role == roles.AdminAPI ||
    user_access.some((user) => user.email === req.user.email)
  ) {
    if (req.user.reason == id || req.user.role == roles.Admin) {
      try {
        const case_ = await caseService.getCase(id);
        if (req.user.reason == id && case_.status == 4) {
          await caseService.updateCaseStatus(5, req.body.id, case_.user_id);
        }
        if (req.user.reason == id && case_.status == 7) {
          return res.status(200).json({
            success: true,
            redirect: true,
            exported: true,
          });
        }
        return res.status(200).json({
          success: true,
          message: "Case fetched",
          case: case_,
          exported: true,
        });
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    } else if (
      req.user.role == roles.LawfirmAdmin ||
      req.user.role == roles.LawfirmSuperAdmin ||
      req.user.role == roles.LawfirmUser ||
      req.user.role == roles.AdminAPI
    ) {
      try {
        const case_ = await caseService.getCase(id);
        const user = await smsService.getInfo(id);
        case_.userName = user.first_name;
        case_.userEmail = user.email;
        if (case_.lf_id == req.user.lf_id) {
          return res.status(200).json({
            success: true,
            message: "Case fetched",
            case: case_,
            exported: true,
          });
        } else {
          return res.status(500).json({
            success: false,
            message: "Permission denied!",
          });
        }
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    } else {
      return res.status(500).json({
        success: false,
        message: "Permission denied!",
      });
    }
  } else {
    if (req.user.reason == id || req.user.role == roles.Admin) {
      try {
        const case_ = await caseService.getCase(id);
        if (req.user.reason == id && case_.status == 4) {
          await caseService.updateCaseStatus(5, req.body.id, case_.user_id);
        }
        if (req.user.reason == id && case_.status == 7) {
          return res.status(200).json({
            success: true,
            redirect: true,
            exported: false,
          });
        }
        return res.status(200).json({
          success: true,
          message: "Case fetched",
          case: case_,
          exported: false,
        });
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    } else if (
      req.user.role == roles.LawfirmAdmin ||
      req.user.role == roles.LawfirmSuperAdmin ||
      req.user.role == roles.LawfirmUser ||
      req.user.role == roles.AdminAPI
    ) {
      try {
        const case_ = await caseService.getCase(id);
        const user = await smsService.getInfo(id);
        case_.userName = user.first_name;
        case_.userEmail = user.email;
        if (case_.lf_id == req.user.lf_id) {
          return res.status(200).json({
            success: true,
            message: "Case fetched",
            case: case_,
            exported: false,
          });
        } else {
          return res.status(500).json({
            success: false,
            message: "Permission denied!",
          });
        }
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    } else {
      return res.status(500).json({
        success: false,
        message: "Permission denied!",
      });
    }
  }
});

export const getDetailCaseAnswerById = catchAsync(async (req, res) => {
  const user_access = await caseService.getAllGranted(req.body.id);
  const result = await caseService.getDetailCaseAnswerById(req.body.id);
  if (
    req.user.role == roles.LawfirmSuperAdmin ||
    user_access.some((user) => user.email === req.user.email)
  ) {
    return res.status(200).json({
      success: true,
      message: "Case fetched",
      data: { result: result.ans, status: result.status, exported: true },
    });
  } else
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  // return res.status(200).json({
  //   success: true,
  //   message: "Case fetched",
  //   data: { result: result.ans, status: result.status, exported: false },
  // });
});

export const getAllCase = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Client) {
    try {
      const { page, size, keyword, status } = req.body;
      const result = await caseService.getAllCase(
        page,
        size,
        keyword,
        status,
        req.user.user_id,
        null
      );
      return res.status(200).json({
        success: true,
        message: "ok",
        data: result,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else if (
    role == roles.LawfirmSuperAdmin ||
    role == roles.LawfirmAdmin ||
    role == roles.LawfirmUser ||
    role == roles.AdminAPI
  ) {
    try {
      const { page, size, keyword, status, user_id, tab, email } = req.body;
      const result = await caseService.getAllCase(
        page,
        size,
        keyword,
        status,
        tab,
        user_id,
        req.user.lf_id,
        email
      );
      return res.status(200).json({
        success: true,
        message: "ok",
        data: result,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else if (role == roles.Admin || role == roles.AdminAPI) {
    try {
      const { page, size, keyword, status, lf_id, user_id, tab } = req.body;
      const result = await caseService.getAllCase(
        page,
        size,
        keyword,
        status,
        tab,
        user_id,
        lf_id
      );
      return res.status(200).json({
        success: true,
        message: "ok",
        data: result,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

//=============================================================

export const deleteCase = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (
    role == roles.LawfirmSuperAdmin ||
    role == roles.LawfirmAdmin ||
    role == roles.LawfirmUser
  ) {
    const { id } = req.body;
    try {
      const qtn = await caseService.deleteCase(id);
      return res.status(200).json({
        success: true,
        message: "Case deleted",
        qtn,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const updateCaseStatus = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin || role == roles.LawfirmSuperAdmin || role == roles.AdminAPI) {
    let check = await caseService.getCase(req.body.id);
    if (check.length == 0) {
      return res.status(400).json({
        success: false,
        message: "Case does not exist",
      });
    } else {
      try {
        const qtn = await caseService.updateCaseStatus(
          req.body.status,
          req.body.id,
          req.user.user_id //lf_id
        );
        return res.status(200).json({
          success: true,
          message: "Case updated",
        });
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});
export const updateAnswer = catchAsync(async (req, res) => {
  const { id } = req.body;
  if (req.user.reason == id) {
    let check = await caseService.getCase(req.body.id);
    if (Object.keys(check).length == 0) {
      return res.status(400).json({
        success: false,
        message: "Case does not exist",
      });
    } else {
      try {
        const qtn = await caseService.updateAnswer(req.body, check.user_id);
        // if (req.body?.answer.length > 0) {
        //   await caseService.updateCaseStatus(6, req.body.id, check.user_id);
        // }
        return res.status(200).json({
          success: true,
          message: "Answer updated",
        });
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const updateAnswerSubmitted = catchAsync(async (req, res) => {
  if (
    req.user.role == roles.LawfirmAdmin ||
    req.user.role == roles.LawfirmSuperAdmin ||
    req.user.role == roles.AdminAPI ||
    (await caseService.checkPermission(req.body.id, req.user.email)) == 1
  ) {
    let check = await caseService.getCase(req.body.id);
    if (Object.keys(check).length == 0) {
      return res.status(400).json({
        success: false,
        message: "Case does not exist",
      });
    } else {
      try {
        const qtn = await caseService.updateAnswerSubmitted(
          req.body,
          check.user_id
        );
        return res.status(200).json({
          success: true,
          message: "Answer updated",
        });
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const submitCase = catchAsync(async (req, res) => {
  const { id } = req.body;

  if (req.user.reason == id) {
    let check = await caseService.getCase(req.body.id);
    if (Object.keys(check).length == 0) {
      return res.status(400).json({
        success: false,
        message: "Case does not exist",
      });
    } else {
      try {
        logger.info("1");
        const case_ = await caseService.updateCaseStatus(
          7,
          req.body.id,
          check.user_id
        );

        logger.info("2");
        if (check.user_id) {
          caseService.updateModalAnswer(req.body.id);
        }

        logger.info("3");
        await caseService.updateInvoice(req.body.id);

        logger.info("4");

        let listReceiver = await caseService.getAllReceiverSubmitCaseMail(
          check.lf_id,
          req.body.id
        );

        logger.info("5");
        for (let user of listReceiver) {
          try {
            await smsService.sendMailSubmitCase(
              user.first_name,
              user.email,
              user.role,
              req.body.id
            );
          } catch (error) {
            console.error(
              `Failed to send email for user_id: ${user.user_id}`,
              error
            );
          }
        }

        logger.info("6");

        return res.status(200).json({
          success: true,
          message: "Case submitted",
        });
      } catch (error) {
        console.log(error);
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const getCaseByUserId = catchAsync(async (req, res) => {
  try {
    const { user_id } = req.body;
    let case_ = await caseService.getCaseByUserId(user_id);
    return res.status(200).json({
      success: true,
      message: "Case fetched",
      case: case_,
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message,
    });
  }
});

export const getCaseByQuestionaireId = catchAsync(async (req, res) => {
  try {
    const { qtn_id } = req.body;
    let case_ = await caseService.getCaseByQuestionaireId(qtn_id);
    return res.status(200).json({
      success: true,
      message: "Case fetched",
      case: case_,
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message,
    });
  }
});
export const getCaseSumByUserId = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (
    role == roles.LawfirmSuperAdmin ||
    role == roles.LawfirmAdmin ||
    role == roles.LawfirmUser ||
    role == roles.AdminAPI
  ) {
    try {
      const { page, size, keyword } = req.query;
      const result = await caseService.getCaseSumByUserId(
        page,
        size,
        keyword,
        req.user.lf_id
      );
      return res.status(200).json({
        success: true,
        message: "Case fetched",
        data: result,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const callMe = catchAsync(async (req, res) => {
  if (!req.body.phone) {
    return res.status(400).json({
      success: false,
      message: "Phone number is required",
    });
  } else if (!validator.isMobilePhone(req.body.phone, "any")) {
    return res.status(400).json({
      success: false,
      message: "Invalid phone number",
    });
  } else if (!req.body.best_time) {
    return res.status(400).json({
      success: false,
      message: "Best time is required",
    });
  } else if (!req.body.case_id) {
    return res.status(400).json({
      success: false,
      message: "Case id is required",
    });
  }

  try {
    const result = await caseService.callMe(
      req.body.case_id,
      req.body.phone,
      req.body.best_time
    );
    return res.status(200).json({
      success: true,
      message: "Noti sended",
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message,
    });
  }
});

export const downloadAfterSubmit = catchAsync(async (req, res) => {
  if (req.user.reason == req.query.case_id) {
    try {
      const pdfStream = await caseService.downloadAfterSubmit(
        req.query.case_id
      );
      res
        .writeHead(200, {
          "Content-Length": Buffer.byteLength(pdfStream),
          "Content-Type": "application/pdf",
          "Content-Disposition": `attachment; filename=case-${Date.now()}.pdf`,
        })
        .end(pdfStream);
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const downloadPDF = catchAsync(async (req, res) => {
  if (
    req.user.role == roles.LawfirmSuperAdmin ||
    req.user.reason == req.body.case_id ||
    (await caseService.checkPermission(req.body.case_id, req.user.email)) == 1
  ) {
    try {
      const pdfStream = await caseService.downloadPDF(req.body, req.user.role);
      res
        .writeHead(200, {
          "Content-Length": Buffer.byteLength(pdfStream),
          "Content-Type": "application/pdf",
          "Content-Disposition": `attachment; filename=case-${Date.now()}.pdf`,
        })
        .end(pdfStream);
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const grantPermission = catchAsync(async (req, res) => {
  if (req.user.role == roles.LawfirmSuperAdmin) {
    const { case_id, email } = req.body;
    let result = await caseService.grantPermission(case_id, email);
    if (result == 1) {
      return res.status(200).json({
        success: true,
        message: "Permission granted successfully",
      });
    } else if (result == 2) {
      return res.status(400).json({
        success: false,
        message: "The user does not belong to your law firm",
      });
    } else if (result == 3) {
      return res.status(400).json({
        success: false,
        message:
          "Permission has already been granted to this email for the specified case.",
      });
    } else {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }
  } else {
    return res.status(403).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const denyPermission = catchAsync(async (req, res) => {
  if (req.user.role == roles.LawfirmSuperAdmin) {
    const { case_id, email } = req.body;
    let result = await caseService.denyPermission(case_id, email);
    if (result == 1) {
      return res.status(200).json({
        success: true,
        message: "Permission denied successfully",
      });
    } else {
      return res.status(400).json({
        success: false,
        message: "Error in deny permission",
      });
    }
  } else {
    return res.status(403).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const getAllGranted = catchAsync(async (req, res) => {
  if (req.user.role == roles.LawfirmSuperAdmin) {
    const { case_id } = req.body;
    let result = await caseService.getAllGranted(case_id);
    return res.status(200).json({
      success: true,
      message: "Granted fetch",
      result: result,
    });
  } else {
    return res.status(403).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const getModalAnswer = catchAsync(async (req, res) => {
  const { id } = req.body;
  const user_access = await caseService.getAllGranted(id);

  if (
    req.user.reason == id ||
    req.user.role == roles.Admin ||
    user_access.some((user) => user.email === req.user.email)
  ) {
    try {
      let result = await caseService.getModalAnswer(id);
      return res.status(200).json({
        success: true,
        message: "Modal answer fetch",
        result: result,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(403).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

// export const getPartyFromPMS = catchAsync(async (req, res) => {
//   let result = await caseService.getDataPartyFromPMS(req.body);
//   return res.status(200).json({
//     success: true,
//     message: "done",
//     data: result,
//   });
// });

// export const getDataPMS = catchAsync(async (req, res) => {
//   let result = await caseService.getDataPMS(req.query.keyword);
//   return res.status(200).json({
//     success: true,
//     message: "done",
//     data: result,
//   });
// });

export const getCaseIdFromPMS = catchAsync(async (req, res) => {
  if (!req.query.filter) {
    return res.status(400).json({
      success: false,
      message: "Filter is required",
    });
  } else if (!Object.values(filter_pms).includes(req.query.filter)) {
    return res.status(400).json({
      success: false,
      message: "Invalid filter",
    });
  } else if (!req.query.page) {
    return res.status(400).json({
      success: false,
      message: "Page is required",
    });
  } else if (
    req.query.filter === filter_pms.Name &&
    (!req.query.first_name || !req.query.last_name)
  ) {
    return res.status(400).json({
      success: false,
      message: "First name and last name are required",
    });
  } else if (!req.query.lf_id) {
    return res.status(400).json({
      success: false,
      message: "Lawfirm id is required",
    });
  }
  let result = await caseService.getCaseIdFromPMS(
    req.query.first_name,
    req.query.last_name,
    req.query.keyword,
    req.query.filter,
    req.query.page,
    req.query.lf_id
  );
  return res.status(200).json({
    success: true,
    message: "done",
    data: result,
  });
});

export const getPrefixFromCaseId = catchAsync(async (req, res) => {
  let result = await caseService.getPrefixFromCaseId(req.query.case_id);
  return res.status(200).json({
    success: true,
    data: result ? result : null,
  });
});

export const exportPDFToPMS = catchAsync(async (req, res) => {
  if (
    // req.user.role == roles.LawfirmAdmin ||
    req.user.role == roles.LawfirmSuperAdmin ||
    req.user.role == roles.AdminAPI ||
    (await caseService.checkPermission(req.body.id, req.user.email)) == 1
  ) {
    try {
      const file = await caseService.downloadPDF(req.body);
      const fileNames = await caseService.extractFileNames(req.body);

      const attachments = [];

      const filesDir = path.join(__dirname, "../../static/file");

      for (const fileName of fileNames) {
        const filePath = path.join(filesDir, fileName);

        if (fs.existsSync(filePath)) {
          // Kiểm tra xem file có tồn tại hay không
          attachments.push(`${fileName}`);
        } else {
          console.warn(`File not found: ${filePath}`);
        }
      }
      const form = new FormData();

      form.append("FileData", file, {
        filename: `case-${req.body.case_id_pms}.pdf`,
        contentType: "application/pdf",
      });

      // let attachmentIndex = 0;
      // for (const fileName of fileNames) {
      //   const filePath = path.join(filesDir, fileName);
      //   if (fs.existsSync(filePath)) {
      //     const fileContent = fs.readFileSync(filePath);
      //     form.append(`Attachment[${attachmentIndex}]`, fileContent, {
      //       filename: fileName,
      //       contentType: "application/pdf",
      //     });
      //     attachmentIndex++;
      //   }
      // }

      const activePiecesWebhookUrl = process.env.MIDDLEWARE_PDF_PPR_TO_PMS;
      await axios.post(
        activePiecesWebhookUrl,
        {
          CaseCode: req.body.case_id_pms ?? req.body.id,
          CaseName: req.body.case_id_pms
            ? `case-${req.body.case_id_pms}.pdf`
            : `case-${req.body.id}.pdf`,
          FileData: form,
          lf_id: req.body.lf_id,
        },
        {
          headers: {
            ...form.getHeaders(),
          },
        }
      );

      const activePiecesWebhookUrl_ = process.env.MIDDLEWARE_SEND_ATTACHMENT;
      await axios.post(
        activePiecesWebhookUrl_,
        {
          CaseCode: req.body.case_id_pms ?? req.body.id,
          lf_id: req.body.lf_id,
          Attachments: attachments,
        },
        {
          headers: {
            ...form.getHeaders(),
          },
        }
      );

      return res.status(200).json({
        success: true,
        message: "File sent to PMS successfully",
      });
    } catch (error) {
      console.log(error);
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const exportDataToPMS = catchAsync(async (req, res) => {
  if (
    req.user.role == roles.LawfirmAdmin ||
    req.user.role == roles.LawfirmSuperAdmin ||
    req.user.role == roles.AdminAPI
    // || (await caseService.checkPermission(req.body.id, req.user.email)) == 1
  ) {
    const check = await caseService.getCase(req.body.id);
    if (Object.keys(check).length == 0) {
      return res.status(400).json({
        success: false,
        message: "Case does not exist",
      });
    } else {
      try {
        let result = await caseService.exportDataToPMS(req.body);
        result = {
          ...result,
          case_id: req.body.case_id_pms ?? req.body.id,
          lf_id: req.body.lf_id,
          qtn_id: req.body.qtn_id,
        };
        const activePiecesWebhookUrl = process.env.MIDDLEWARE_DATA_TO_PMS;
        await axios.post(activePiecesWebhookUrl, {
          result,
        });
        let result1 = await caseService.exportDataToPMS1(check);
        result1 = {
          ...result1,
          case_id: req.body.case_id_pms ?? req.body.id,
          lf_id: req.body.lf_id,
          qtn_id: req.body.qtn_id,
        };
        let res1 = await axios.post(process.env.MIDDLEWARE_SEND_DATA, result1);
        if (res1.data.url != null) {
          return res.status(200).json({
            success: true,
            message: "Data exported successfully",
            url: res1.data.url,
          });
        }
        return res.status(200).json({
          success: true,
          message: "Data exported successfully",
          // result,
        });
      } catch (error) {
        console.log(error);
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const createCaseFromPMS = catchAsync(async (req, res) => {
  try {
    const { case_name, qtn_id, pms_id, case_id_pms, desc } = req.body;
    let case_ = await caseService.createCaseFromPMS(
      case_name,
      desc,
      qtn_id,
      pms_id,
      null,
      case_id_pms
    );
    if (case_ == 0) {
      return res.status(200).json({
        success: false,
        message: "User not found",
      });
    } else if (case_ == 1) {
      return res.status(200).json({
        success: false,
        message: "Questionaire not found",
      });
    } else if (case_ == 2) {
      return res.status(200).json({
        success: false,
        message: "Questionaire is not active",
      });
    }
    if (case_.check == 1) {
      let userInfo = await smsService.getInfo(case_.case_id);
      let result = smsService.sendLinkCaseVerify(
        userInfo.first_name,
        userInfo.email,
        userInfo.phone.slice(-4),
        case_.case_id,
        req.user.lf_id,
        userInfo.user_id,
        req.user
      );
    } else if (case_.check == 2) {
      let userInfo = await smsService.getInfo(case_.case_id);
      let result = smsService.sendChaserMail(
        userInfo.first_name,
        userInfo.email,
        userInfo.phone.slice(-4),
        case_.case_id,
        req.user.lf_id,
        userInfo.user_id,
        req.user
      );
    }
    return res.status(200).json({
      success: true,
      message: "Case created",
      case_id: case_.case_id,
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message,
    });
  }
});

export const getPartiesForAssignQtn = catchAsync(async (req, res) => {
  try {
    let result = await caseService.getPartiesForAssignQtn(
      req.body.lf_id,
      req.body.case_id
    );
    return res.status(200).json({
      success: true,
      message: "done",
      data: result,
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message,
    });
  }
});

export const updateEmailorPhoneParty = catchAsync(async (req, res) => {
  const { email, phone, first_name, country_code, last_name, case_id } =
    req.body;
  if (email == null || email == undefined || email == "") {
    return res.status(400).json({
      success: false,
      message: "Email is required",
    });
  } else if (!validator.isEmail(email)) {
    return res.status(400).json({
      success: false,
      message: "Invalid email",
    });
  } else if (phone == null || phone == undefined || phone == "") {
    return res.status(400).json({
      success: false,
      message: "Phone is required",
    });
  } else if (
    first_name == null ||
    first_name == undefined ||
    first_name == ""
  ) {
    return res.status(400).json({
      success: false,
      message: "First name is required",
    });
  } else if (last_name == null || last_name == undefined) {
    return res.status(400).json({
      success: false,
      message: "Last name is required",
    });
  } else if (case_id == null || case_id == undefined || case_id == "") {
    return res.status(400).json({
      success: false,
      message: "Case id is required",
    });
  } else if (
    country_code == null ||
    country_code == undefined ||
    country_code == ""
  ) {
    return res.status(400).json({
      success: false,
      message: "Country code is required",
    });
  }
  try {
    let result = await caseService.updateEmailorPhoneParty(
      email,
      phone,
      first_name,
      last_name,
      case_id,
      req.user.lf_id,
      country_code
    );
    return res.status(200).json({
      success: true,
      message: "done",
      data: result,
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message,
    });
  }
});

export const assignQtnToParty = catchAsync(async (req, res) => {
  const assigned_by = req.user.user_id;
  const {
    case_name,
    cases,
    token_,
    qtn_id,
    case_id,
    first_name,
    last_name,
    desc,
  } = req.body;

  if (
    !req.user.lf_id ||
    req.user.role == roles.Client ||
    req.user.role == roles.Admin
  ) {
    return res.status(400).json({
      success: false,
      message: "Permission denied",
    });
  } else if (req.user.lf_id && (await checkConnectPMS(req.user.lf_id)) != 1) {
    return res.status(400).json({
      success: false,
      message: "Not connected to PMS",
    });
  }
  try {
    let result = await caseService.assignQtnToParty(
      case_name,
      desc,
      cases,
      qtn_id,
      assigned_by,
      case_id,
      first_name,
      last_name,
      token_
    );
    if (result.check == 1) {
      let userInfo = await smsService.getInfoPMS(result.case_id);
      smsService.sendLinkCaseVerifyPMS(
        userInfo.first_name,
        userInfo.email,
        userInfo.phone.slice(-4),
        result.case_id,
        req.user.lf_id,
        userInfo.user_id,
        req.user
      );
      return res.status(200).json({
        success: true,
        message: "done",
        data: result,
      });
    } else if (result.check == 2) {
      let userInfo = await smsService.getInfoPMS(result.case_id);
      await smsService.sendChaserMailPMS(
        userInfo.first_name,
        userInfo.email,
        userInfo.phone.slice(-4),
        result.case_id,
        req.user.lf_id,
        userInfo.user_id,
        req.user
      );
      return res.status(200).json({
        success: true,
        message: "done",
        data: result,
      });
    }
    return res.status(200).json({
      success: true,
      message: "done",
      data: result,
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message,
    });
  }
});

export const checkInfoParty = catchAsync(async (req, res) => {
  const { first_name, last_name, case_id } = req.body;
  try {
    let result = await caseService.checkInfoParty(
      first_name,
      last_name,
      case_id,
      req.user.lf_id
    );
    return res.status(200).json({
      success: true,
      message: "done",
      data: result,
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message,
    });
  }
});

export const uploadDocument = catchAsync(async (req, res) => {
  // const allowedFileTypes = [
  //   "application/pdf",
  //   "application/msword",
  //   "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  //   "application/vnd.ms-excel",
  //   "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  // ];

  // if (!allowedFileTypes.includes(req.file.mimetype)) {
  //   return res.status(400).json({
  //     success: false,
  //     message: "Invalid file type. Only PDF, DOC, and Excel files are allowed.",
  //   });
  // }

  if (
    req.user.reason == req.body.case_id ||
    req.user.role == roles.Admin ||
    req.user.role == roles.LawfirmAdmin ||
    req.user.role == roles.LawfirmSuperAdmin ||
    (await caseService.checkPermission(req.body.case_id, req.user.email)) == 1
  ) {
    try {
      let result = await caseService.uploadDocument(req.file);
      return res.status(200).json({
        success: true,
        message: "done",
        data: result,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const downloadDocument = catchAsync(async (req, res) => {
  if (
    req.user.reason == req.query.case_id ||
    req.user.role == roles.LawfirmAdmin ||
    req.user.role == roles.Admin ||
    req.user.role == roles.LawfirmSuperAdmin ||
    (await caseService.checkPermission(req.body.case_id, req.user.email)) == 1
  ) {
    const file = await caseService.downloadDocument(req.query.file);
    return res.status(200).json({
      success: true,
      message: "done",
      data: file,
    });
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const updateStatusExported = catchAsync(async (req, res) => {
  if (
    req.user.role == roles.LawfirmAdmin ||
    req.user.role == roles.LawfirmSuperAdmin
  ) {
    try {
      let result = await caseService.updateStatusExported(req.body.case_id);
      return res.status(200).json({
        success: true,
        message: "done",
        data: result,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const generateSpreadsheet = catchAsync(async (req, res) => {
  const user_access = await caseService.getAllGranted(req.body.case_id);
  if (
    req.user.role == roles.LawfirmSuperAdmin ||
    req.user.role == roles.AdminAPI ||
    user_access.some((user) => user.email === req.user.email)
  ) {
    try {
      if (req.body.check == 1) {
        const excelBuffer = await caseService.generateSpreadsheet(
          req.body.case_id
        );
        res.status(200).json({
          success: true,
          message: "done",
          data: excelBuffer,
        });
      } else {
        const excelBuffer = await caseService.generateSpreadsheet1(
          req.body.case_id
        );
        res.status(200).json({
          success: true,
          message: "done",
          data: excelBuffer,
        });
      }
    } catch (error) {
      console.log(error);

      return res.status(400).json({
        success: false,
        message: "Format not supported",
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const getCaseData = catchAsync(async (req, res) => {
  if (req.user.role == roles.AdminAPI) {
    try {
      let case_ = await caseService.getCase(req.body.case_id);
      let result = await caseService.exportDataToPMS1(case_);
      return res.status(200).json({
        success: true,
        data: result,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  }
});
